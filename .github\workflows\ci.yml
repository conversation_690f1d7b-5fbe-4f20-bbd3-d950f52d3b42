name: CI

on:
  push:
    branches: [ develop, main, release/*, hotfix/* ]
  pull_request:
    branches: [ develop, main ]
  schedule:
    - cron: '0 3 * * 1' # Every Monday at 03:00 UTC

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'yarn'
      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run ESLint and TypeScript checks
        run: |
          yarn biome check .
          yarn tsc --noEmit

      - name: Build
        run: |
          export NODE_OPTIONS="--max_old_space_size=4096"
          yarn build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}

  dependencies:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Update dependencies
        run: |
          yarn install --frozen-lockfile
          yarn upgrade-interactive --latest --yes || true
      - name: Commit and create PR
        uses: peter-evans/create-pull-request@v6
        with:
          commit-message: 'chore(deps): update dependencies [automated]'
          title: 'chore(deps): update dependencies [automated]'
          body: 'Automated dependency update via GitHub Actions.'
          branch: 'chore/auto-deps-update'
          delete-branch: true