"use client";

import { UserPlus } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/lib/supabase";

interface CourseInstructorManagerProps {
  courseId: string;
  currentInstructorId: string | null;
  onInstructorChange: (instructorId: string) => void;
}

interface Instructor {
  id: string;
  first_name: string;
  last_name: string;
  role: string;
}

interface Student {
  id: string;
  first_name: string;
  last_name: string;
}

export default function CourseInstructorManager({
  courseId,
  currentInstructorId,
  onInstructorChange,
}: CourseInstructorManagerProps) {
  const [loading, setLoading] = useState<boolean>(true);
  const [instructors, setInstructors] = useState<Array<Instructor>>([]); // Define Instructor type
  const [students, setStudents] = useState<Array<Student>>([]); // Define Student type
  const [selectedStudent, setSelectedStudent] = useState<string>("");
  const [isPromotingStudent, setIsPromotingStudent] = useState<boolean>(false);
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [mounted, setMounted] = useState<boolean>(false);

  // Asegurarse de que el componente solo se renderice en el cliente
  useEffect(() => {
    setMounted(true);
  }, []);

  // Cargar instructores y estudiantes
  useEffect(() => {
    async function loadData() {
      setLoading(true);
      try {
        // Cargar todos los instructores (usuarios con rol admin o instructor)
        const { data: instructorsData, error: instructorsError } =
          await supabase
            .from("users")
            .select("id, first_name, last_name, role")
            .in("role", ["admin", "instructor"])
            .order("first_name", { ascending: true });

        if (instructorsError) {
          console.error("Error al cargar instructores:", instructorsError);
          throw instructorsError;
        }

        setInstructors(instructorsData || []);

        // Cargar todos los estudiantes
        const { data: studentsData, error: studentsError } = await supabase
          .from("users")
          .select("id, first_name, last_name")
          .eq("role", "student")
          .order("first_name", { ascending: true });

        if (studentsError) {
          console.error("Error al cargar estudiantes:", studentsError);
          throw studentsError;
        }

        setStudents(studentsData || []);

        // Si estamos creando un nuevo curso y no hay instructor seleccionado,
        // intentar seleccionar automáticamente al usuario administrador actual
        if (
          courseId === "new" &&
          (!currentInstructorId || currentInstructorId === "none")
        ) {
          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (user) {
            const adminInstructor = instructorsData?.find(
              (instructor) =>
                instructor.id === user.id && instructor.role === "admin",
            );
            if (adminInstructor) {
              onInstructorChange(adminInstructor.id);
            }
          }
        }
      } catch (error) {
        const errMsg =
          error instanceof Error ? error.message : "Error cargando datos.";
        console.error("Error loading data:", error);
        toast({
          title: "Error al cargar datos",
          description: errMsg,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [courseId, currentInstructorId, onInstructorChange]);

  // Promover estudiante a instructor
  const promoteStudentToInstructor = async () => {
    if (!selectedStudent) return;

    setIsPromotingStudent(true);
    try {
      // Obtener datos del estudiante antes de promoverlo
      const { data: studentBeforeData, error: studentBeforeError } =
        await supabase
          .from("users")
          .select("id, first_name, last_name")
          .eq("id", selectedStudent)
          .single();

      if (studentBeforeError) {
        console.error(
          "Error al obtener datos del estudiante:",
          studentBeforeError,
        );
        throw studentBeforeError;
      }

      // Actualizar el rol del estudiante a instructor
      const { error } = await supabase
        .from("users")
        .update({ role: "instructor" })
        .eq("id", selectedStudent);

      if (error) {
        console.error("Error al promover estudiante:", error);
        throw error;
      }

      // Crear objeto con los datos del estudiante promovido
      const promotedInstructor = {
        id: studentBeforeData.id,
        first_name: studentBeforeData.first_name,
        last_name: studentBeforeData.last_name,
        role: "instructor",
      };

      // Actualizar la lista de instructores
      setInstructors([...instructors, promotedInstructor]);

      // Eliminar el estudiante de la lista de estudiantes
      setStudents(students.filter((student) => student.id !== selectedStudent));

      // Resetear selección
      setSelectedStudent("");

      // Cerrar el diálogo
      setIsDialogOpen(false);

      // Seleccionar automáticamente al nuevo instructor si no hay instructor seleccionado
      if (!currentInstructorId || currentInstructorId === "none") {
        onInstructorChange(promotedInstructor.id);
      }

      toast({
        title: "Éxito",
        description: "Estudiante promovido a instructor correctamente",
      });
    } catch (error) {
      const errMsg =
        error instanceof Error
          ? error.message
          : "Error promoviendo estudiante.";
      console.error("Error promoting student:", error);
      toast({
        title: "Error al promover estudiante",
        description: errMsg,
        variant: "destructive",
      });
    } finally {
      setIsPromotingStudent(false);
    }
  };

  // Si no está montado, devolvemos un div vacío para evitar errores de hidratación
  if (!mounted) {
    return (
      <div className="h-9 flex items-center px-3 border rounded-md bg-gray-50">
        <span className="text-sm text-gray-500">Cargando...</span>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-end gap-2">
        <div className="flex-1">
          {loading ? (
            <div className="h-9 flex items-center px-3 border rounded-md bg-gray-50">
              <span className="text-sm text-gray-500">
                Cargando instructores...
              </span>
            </div>
          ) : (
            <Select
              value={currentInstructorId || "none"}
              onValueChange={onInstructorChange}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar instructor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Sin instructor asignado</SelectItem>
                {instructors.map((instructor) => (
                  <SelectItem key={instructor.id} value={instructor.id}>
                    {instructor.role === "admin" &&
                    !instructor.first_name &&
                    !instructor.last_name
                      ? "Admin Domus"
                      : `${instructor.first_name || ""} ${instructor.last_name || ""}`.trim()}
                    <span className="ml-2 text-xs text-gray-500">
                      {instructor.role === "admin" ? "(Admin)" : "(Instructor)"}
                    </span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              type="button"
              size="sm"
              variant="outline"
              className="flex items-center gap-1"
              disabled={loading}
            >
              <UserPlus className="h-4 w-4" />
              Nuevo
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Promover Estudiante a Instructor</DialogTitle>
              <DialogDescription>
                Selecciona un estudiante para convertirlo en instructor. Esta
                acción cambiará su rol en el sistema.
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <Label htmlFor="student">Estudiante</Label>
              <Select
                value={selectedStudent}
                onValueChange={setSelectedStudent}
                disabled={students.length === 0}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={
                      students.length === 0
                        ? "No hay estudiantes disponibles"
                        : "Seleccionar estudiante"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {students.length > 0 ? (
                    students.map((student) => (
                      <SelectItem key={student.id} value={student.id}>
                        {`${student.first_name} ${student.last_name}`}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>
                      No hay estudiantes disponibles
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <DialogFooter>
              <Button
                type="button"
                onClick={promoteStudentToInstructor}
                disabled={!selectedStudent || isPromotingStudent}
              >
                {isPromotingStudent
                  ? "Promoviendo..."
                  : "Promover a Instructor"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
