/**
 * @fileoverview Unit tests for BaseService class
 *
 * Tests the core functionality of the BaseService class including
 * response helpers, validation methods, error handling, and utility functions.
 */

import type { RepositoryFactory } from "../../repositories";
import {
  BaseService,
  type PaginationOptions,
  type ServiceResponse,
} from "../base-service";

// Mock repository factory
const mockRepositoryFactory = {} as RepositoryFactory;

// Test implementation of BaseService
class TestService extends BaseService {
  constructor() {
    super(mockRepositoryFactory);
  }

  // Expose protected methods for testing
  public testSuccess<T>(data: T, message?: string): ServiceResponse<T> {
    return this.success(data, message);
  }

  public testError(
    code: string,
    message: string,
    details?: unknown,
    field?: string,
  ): ServiceResponse {
    return this.error(code, message, details, field);
  }

  public testValidationError(field: string, message: string): ServiceResponse {
    return this.validationError(field, message);
  }

  public testValidateRequired(
    value: unknown,
    fieldName: string,
  ): ServiceResponse | null {
    return this.validateRequired(value, fieldName);
  }

  public testValidateEmail(email: string): ServiceResponse | null {
    return this.validateEmail(email);
  }

  public testValidateLength(
    value: string,
    fieldName: string,
    min?: number,
    max?: number,
  ): ServiceResponse | null {
    return this.validateLength(value, fieldName, min, max);
  }

  public testCalculatePagination(total: number, page?: number, limit?: number) {
    return this.calculatePagination(total, page, limit);
  }

  public testValidatePaginationOptions(options: PaginationOptions) {
    return this.validatePaginationOptions(options);
  }

  public testGenerateId(): string {
    return this.generateId();
  }

  public testSanitizeString(value: string): string {
    return this.sanitizeString(value);
  }

  public testIsValidDate(date: string): boolean {
    return this.isValidDate(date);
  }
}

describe("BaseService", () => {
  let service: TestService;

  beforeEach(() => {
    service = new TestService();
  });

  describe("Response Helpers", () => {
    it("should create a successful response", () => {
      const data = { id: "123", name: "Test" };
      const message = "Success message";

      const result = service.testSuccess(data, message);

      expect(result).toEqual({
        success: true,
        data,
        message,
      });
    });

    it("should create a successful response without message", () => {
      const data = { id: "123" };

      const result = service.testSuccess(data);

      expect(result).toEqual({
        success: true,
        data,
        message: undefined,
      });
    });

    it("should create an error response", () => {
      const code = "TEST_ERROR";
      const message = "Test error message";
      const details = { extra: "info" };
      const field = "testField";

      const result = service.testError(code, message, details, field);

      expect(result).toEqual({
        success: false,
        error: {
          code,
          message,
          details,
          field,
        },
      });
    });

    it("should create a validation error response", () => {
      const field = "email";
      const message = "Email is required";

      const result = service.testValidationError(field, message);

      expect(result).toEqual({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message,
          details: null,
          field,
        },
      });
    });
  });

  describe("Validation Helpers", () => {
    describe("validateRequired", () => {
      it("should return null for valid values", () => {
        expect(service.testValidateRequired("test", "field")).toBeNull();
        expect(service.testValidateRequired(123, "field")).toBeNull();
        expect(service.testValidateRequired(true, "field")).toBeNull();
        expect(service.testValidateRequired([], "field")).toBeNull();
      });

      it("should return error for invalid values", () => {
        const fieldName = "testField";

        expect(service.testValidateRequired(null, fieldName)).toEqual({
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: `${fieldName} is required`,
            details: null,
            field: fieldName,
          },
        });

        expect(service.testValidateRequired(undefined, fieldName)).toEqual({
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: `${fieldName} is required`,
            details: null,
            field: fieldName,
          },
        });

        expect(service.testValidateRequired("", fieldName)).toEqual({
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: `${fieldName} is required`,
            details: null,
            field: fieldName,
          },
        });
      });
    });

    describe("validateEmail", () => {
      it("should return null for valid emails", () => {
        expect(service.testValidateEmail("<EMAIL>")).toBeNull();
        expect(
          service.testValidateEmail("<EMAIL>"),
        ).toBeNull();
      });

      it("should return error for invalid emails", () => {
        const invalidEmails = [
          "invalid",
          "test@",
          "@domain.com",
          "test.domain.com",
        ];

        invalidEmails.forEach((email) => {
          expect(service.testValidateEmail(email)).toEqual({
            success: false,
            error: {
              code: "VALIDATION_ERROR",
              message: "Invalid email format",
              details: null,
              field: "email",
            },
          });
        });
      });
    });

    describe("validateLength", () => {
      it("should return null for valid lengths", () => {
        expect(service.testValidateLength("test", "field", 2, 10)).toBeNull();
        expect(
          service.testValidateLength("test", "field", undefined, 10),
        ).toBeNull();
        expect(
          service.testValidateLength("test", "field", 2, undefined),
        ).toBeNull();
      });

      it("should return error for too short values", () => {
        const result = service.testValidateLength("ab", "field", 5, 10);
        expect(result).toEqual({
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "field must be at least 5 characters long",
            details: null,
            field: "field",
          },
        });
      });

      it("should return error for too long values", () => {
        const result = service.testValidateLength(
          "this is too long",
          "field",
          2,
          5,
        );
        expect(result).toEqual({
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "field must be no more than 5 characters long",
            details: null,
            field: "field",
          },
        });
      });
    });
  });

  describe("Pagination Helpers", () => {
    describe("calculatePagination", () => {
      it("should calculate pagination correctly", () => {
        const result = service.testCalculatePagination(25, 2, 10);

        expect(result).toEqual({
          page: 2,
          limit: 10,
          total: 25,
          totalPages: 3,
          hasNext: true,
          hasPrev: true,
        });
      });

      it("should handle first page", () => {
        const result = service.testCalculatePagination(25, 1, 10);

        expect(result).toEqual({
          page: 1,
          limit: 10,
          total: 25,
          totalPages: 3,
          hasNext: true,
          hasPrev: false,
        });
      });

      it("should handle last page", () => {
        const result = service.testCalculatePagination(25, 3, 10);

        expect(result).toEqual({
          page: 3,
          limit: 10,
          total: 25,
          totalPages: 3,
          hasNext: false,
          hasPrev: true,
        });
      });
    });

    describe("validatePaginationOptions", () => {
      it("should validate and normalize pagination options", () => {
        const result = service.testValidatePaginationOptions({
          page: 2,
          limit: 20,
        });

        expect(result).toEqual({
          page: 2,
          limit: 20,
          offset: 20,
        });
      });

      it("should handle invalid values", () => {
        const result = service.testValidatePaginationOptions({
          page: -1,
          limit: 200,
        });

        expect(result).toEqual({
          page: 1,
          limit: 100, // Max limit
          offset: 0,
        });
      });

      it("should use defaults", () => {
        const result = service.testValidatePaginationOptions({});

        expect(result).toEqual({
          page: 1,
          limit: 10,
          offset: 0,
        });
      });
    });
  });

  describe("Utility Methods", () => {
    describe("generateId", () => {
      it("should generate a valid UUID-like string", () => {
        const id = service.testGenerateId();

        expect(id).toMatch(
          /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
        );
      });

      it("should generate unique IDs", () => {
        const id1 = service.testGenerateId();
        const id2 = service.testGenerateId();

        expect(id1).not.toBe(id2);
      });
    });

    describe("sanitizeString", () => {
      it("should trim and normalize whitespace", () => {
        expect(service.testSanitizeString("  hello   world  ")).toBe(
          "hello world",
        );
        expect(service.testSanitizeString("test\n\nstring")).toBe(
          "test string",
        );
        expect(service.testSanitizeString("multiple   spaces")).toBe(
          "multiple spaces",
        );
      });
    });

    describe("isValidDate", () => {
      it("should validate correct date strings", () => {
        expect(service.testIsValidDate("2024-01-15")).toBe(true);
        expect(service.testIsValidDate("2024-01-15T10:30:00Z")).toBe(true);
      });

      it("should reject invalid date strings", () => {
        expect(service.testIsValidDate("invalid-date")).toBe(false);
        expect(service.testIsValidDate("2024-13-01")).toBe(false);
        expect(service.testIsValidDate("")).toBe(false);
      });
    });
  });
});
