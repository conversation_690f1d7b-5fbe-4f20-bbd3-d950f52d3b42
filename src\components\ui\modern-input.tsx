/**
 * @fileoverview Modern Input Component (2025 UI/UX Trends)
 *
 * Componente de input moderno que implementa las tendencias UI/UX 2025:
 * - Efectos de glassmorphism
 * - Animaciones de focus fluidas
 * - Estados visuales mejorados
 * - Micro-interacciones
 * - Diseño minimalista
 */

"use client";

import { cva, type VariantProps } from "class-variance-authority";
import { Eye, EyeOff, Search, X } from "lucide-react";
import * as React from "react";
import { cn } from "@/lib/utils";

const modernInputVariants = cva(
  "flex w-full rounded-xl border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200",
  {
    variants: {
      variant: {
        default:
          "border-input hover:border-primary/50 focus-visible:border-primary",
        glass:
          "glass border-glass-border backdrop-blur-glass hover:bg-glass-background/80",
        neuro: "neuro-inset border-none hover:neuro focus-visible:neuro",
        minimal:
          "border-none bg-transparent border-b-2 border-b-border rounded-none hover:border-b-primary/50 focus-visible:border-b-primary focus-visible:ring-0 focus-visible:ring-offset-0",
        floating:
          "border-input hover:border-primary/50 focus-visible:border-primary pt-6 pb-2",
      },
      size: {
        sm: "h-9 px-3 text-sm",
        default: "h-10 px-3",
        lg: "h-11 px-4 text-base",
        xl: "h-12 px-4 text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ModernInputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof modernInputVariants> {
  label?: string;
  error?: string;
  success?: boolean;
  leftIcon?: React.ComponentType<{ className?: string }>;
  rightIcon?: React.ComponentType<{ className?: string }>;
  clearable?: boolean;
  onClear?: () => void;
}

const ModernInput = React.forwardRef<HTMLInputElement, ModernInputProps>(
  (
    {
      className,
      variant,
      size,
      type = "text",
      label,
      error,
      success,
      leftIcon: LeftIcon,
      rightIcon: RightIcon,
      clearable = false,
      onClear,
      value,
      onChange,
      ...props
    },
    ref,
  ) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [showPassword, setShowPassword] = React.useState(false);
    const [internalValue, setInternalValue] = React.useState(value || "");

    const isPasswordType = type === "password";
    const inputType = isPasswordType && showPassword ? "text" : type;
    const hasValue = Boolean(internalValue || value);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setInternalValue(e.target.value);
      onChange?.(e);
    };

    const handleClear = () => {
      setInternalValue("");
      onClear?.();
      if (onChange) {
        const event = {
          target: { value: "" },
        } as React.ChangeEvent<HTMLInputElement>;
        onChange(event);
      }
    };

    return (
      <div className="relative w-full">
        {/* Label flotante */}
        {label && variant === "floating" && (
          <label
            htmlFor={props.id || name} // Add htmlFor
            className={cn(
              "absolute left-3 transition-all duration-200 pointer-events-none text-muted-foreground",
              isFocused || hasValue
                ? "top-2 text-xs text-primary"
                : "top-1/2 -translate-y-1/2 text-sm",
            )}
          >
            {label}
          </label>
        )}

        {/* Label normal */}
        {label && variant !== "floating" && (
          <label
            htmlFor={props.id || name} // Add htmlFor
            className="block text-sm font-medium text-foreground mb-2"
          >
            {label}
          </label>
        )}

        <div className="relative">
          {/* Icono izquierdo */}
          {LeftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              <LeftIcon className="h-4 w-4" />
            </div>
          )}

          {/* Input */}
          <input
            id={props.id || name} // Add id
            type={inputType}
            className={cn(
              modernInputVariants({ variant, size }),
              LeftIcon && "pl-10",
              (RightIcon || isPasswordType || clearable) && "pr-10",
              error && "border-destructive focus-visible:ring-destructive",
              success && "border-success focus-visible:ring-success",
              "group-hover:shadow-md focus:shadow-lg",
              className,
            )}
            ref={ref}
            value={value !== undefined ? value : internalValue}
            onChange={handleChange}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            {...props}
          />

          {/* Iconos derechos */}
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-1">
            {/* Botón de limpiar */}
            {clearable && hasValue && (
              <button
                type="button"
                onClick={handleClear}
                className="text-muted-foreground hover:text-foreground transition-colors p-0.5 rounded-sm hover:bg-accent"
              >
                <X className="h-3 w-3" />
              </button>
            )}

            {/* Toggle password visibility */}
            {isPasswordType && (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-muted-foreground hover:text-foreground transition-colors p-0.5 rounded-sm hover:bg-accent"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            )}

            {/* Icono derecho personalizado */}
            {RightIcon && !isPasswordType && (
              <div className="text-muted-foreground">
                <RightIcon className="h-4 w-4" />
              </div>
            )}
          </div>

          {/* Indicador de focus */}
          <div
            className={cn(
              "absolute bottom-0 left-0 h-0.5 bg-primary transition-all duration-200",
              isFocused ? "w-full" : "w-0",
            )}
          />
        </div>

        {/* Mensaje de error o éxito */}
        {(error || success) && (
          <div className="mt-2 text-sm">
            {error && (
              <p className="text-destructive flex items-center gap-1">
                <span className="h-1 w-1 rounded-full bg-destructive" />
                {error}
              </p>
            )}
            {success && !error && (
              <p className="text-success flex items-center gap-1">
                <span className="h-1 w-1 rounded-full bg-success" />
                Campo válido
              </p>
            )}
          </div>
        )}
      </div>
    );
  },
);
ModernInput.displayName = "ModernInput";

/**
 * Input de búsqueda moderno
 */
interface ModernSearchInputProps
  extends Omit<ModernInputProps, "leftIcon" | "type"> {
  onSearch?: (value: string) => void;
  searchDelay?: number;
}

export const ModernSearchInput = React.forwardRef<
  HTMLInputElement,
  ModernSearchInputProps
>(({ onSearch, searchDelay = 300, ...props }, ref) => {
  const [searchValue, setSearchValue] = React.useState("");
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  React.useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      onSearch?.(searchValue);
    }, searchDelay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [searchValue, onSearch, searchDelay]);

  return (
    <ModernInput
      ref={ref}
      type="text"
      leftIcon={Search}
      placeholder="Buscar..."
      clearable
      value={searchValue}
      onChange={(e) => setSearchValue(e.target.value)}
      onClear={() => setSearchValue("")}
      {...props}
    />
  );
});
ModernSearchInput.displayName = "ModernSearchInput";

/**
 * Textarea moderno
 */
interface ModernTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof modernInputVariants> {
  label?: string;
  error?: string;
  success?: boolean;
  resize?: boolean;
}

export const ModernTextarea = React.forwardRef<
  HTMLTextAreaElement,
  ModernTextareaProps
>(
  (
    {
      className,
      variant,
      size,
      label,
      error,
      success,
      resize = true,
      ...props
    },
    ref,
  ) => {
    const [isFocused, setIsFocused] = React.useState(false);

    return (
      <div className="relative w-full">
        {label && (
          <label
            htmlFor={props.id || name} // Add htmlFor
            className="block text-sm font-medium text-foreground mb-2"
          >
            {label}
          </label>
        )}

        <div className="relative">
          <textarea
            id={props.id || name} // Add id
            className={cn(
              modernInputVariants({ variant, size }),
              "min-h-[80px]",
              !resize && "resize-none",
              error && "border-destructive focus-visible:ring-destructive",
              success && "border-success focus-visible:ring-success",
              className,
            )}
            ref={ref}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            {...props}
          />

          {/* Indicador de focus */}
          <div
            className={cn(
              "absolute bottom-0 left-0 h-0.5 bg-primary transition-all duration-200",
              isFocused ? "w-full" : "w-0",
            )}
          />
        </div>

        {/* Mensaje de error o éxito */}
        {(error || success) && (
          <div className="mt-2 text-sm">
            {error && (
              <p className="text-destructive flex items-center gap-1">
                <span className="h-1 w-1 rounded-full bg-destructive" />
                {error}
              </p>
            )}
            {success && !error && (
              <p className="text-success flex items-center gap-1">
                <span className="h-1 w-1 rounded-full bg-success" />
                Campo válido
              </p>
            )}
          </div>
        )}
      </div>
    );
  },
);
ModernTextarea.displayName = "ModernTextarea";

export { ModernInput, modernInputVariants };
