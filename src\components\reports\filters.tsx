"use client";

import { Calendar, Download, Filter, RefreshCw, X } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { ReportFilters } from "@/lib/services/reports-service";

interface FiltersProps {
  filters: ReportFilters;
  onFiltersChange: (filters: ReportFilters) => void;
  onExport?: (format: "pdf" | "excel") => void;
  isLoading?: boolean;
}

export function Filters({
  filters,
  onFiltersChange,
  onExport,
  isLoading = false,
}: FiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleFilterChange = (key: keyof ReportFilters, value: string) => {
    const newFilters = { ...filters };
    if (value === "" || value === "all") {
      delete newFilters[key];
    } else {
      newFilters[key] = value;
    }
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  const getActiveFiltersCount = () => {
    return Object.keys(filters).length;
  };

  const formatFilterValue = (key: string, value: string) => {
    switch (key) {
      case "startDate":
        return `Desde: ${new Date(value).toLocaleDateString("es-ES")}`;
      case "endDate":
        return `Hasta: ${new Date(value).toLocaleDateString("es-ES")}`;
      case "status":
        return `Estado: ${value}`;
      case "role":
        return `Rol: ${value}`;
      case "courseId":
        return `Curso: ${value}`;
      case "instructorId":
        return `Instructor: ${value}`;
      default:
        return `${key}: ${value}`;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros de Reportes
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? "Ocultar" : "Mostrar"} Filtros
            </Button>
            {getActiveFiltersCount() > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="text-muted-foreground"
              >
                <X className="h-4 w-4 mr-1" />
                Limpiar
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      {/* Active Filters Display */}
      {getActiveFiltersCount() > 0 && (
        <CardContent className="pt-0">
          <div className="flex flex-wrap gap-2">
            {Object.entries(filters).map(([key, value]) => (
              <Badge
                key={key}
                variant="outline"
                className="flex items-center gap-1"
              >
                {formatFilterValue(key, value)}
                <button
                  type="button"
                  onClick={() =>
                    handleFilterChange(key as keyof ReportFilters, "")
                  }
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </CardContent>
      )}

      {/* Expanded Filters */}
      {isExpanded && (
        <CardContent className="pt-0">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Date Range */}
            <div className="space-y-2">
              <Label htmlFor="startDate" className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Fecha Inicio
              </Label>
              <Input
                id="startDate"
                type="date"
                value={filters.startDate || ""}
                onChange={(e) =>
                  handleFilterChange("startDate", e.target.value)
                }
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate" className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Fecha Fin
              </Label>
              <Input
                id="endDate"
                type="date"
                value={filters.endDate || ""}
                onChange={(e) => handleFilterChange("endDate", e.target.value)}
                className="w-full"
              />
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <Label>Estado</Label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="active">Activo</SelectItem>
                  <SelectItem value="expired">Expirado</SelectItem>
                  <SelectItem value="revoked">Revocado</SelectItem>
                  <SelectItem value="draft">Borrador</SelectItem>
                  <SelectItem value="published">Publicado</SelectItem>
                  <SelectItem value="completed">Completado</SelectItem>
                  <SelectItem value="cancelled">Cancelado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Role Filter */}
            <div className="space-y-2">
              <Label>Rol de Usuario</Label>
              <Select
                value={filters.role || "all"}
                onValueChange={(value) => handleFilterChange("role", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos los roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los roles</SelectItem>
                  <SelectItem value="student">Estudiante</SelectItem>
                  <SelectItem value="instructor">Instructor</SelectItem>
                  <SelectItem value="admin">Administrador</SelectItem>
                  <SelectItem value="company_rep">Representante</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Course Filter */}
            <div className="space-y-2">
              <Label>Curso</Label>
              <Select
                value={filters.courseId || "all"}
                onValueChange={(value) => handleFilterChange("courseId", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos los cursos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los cursos</SelectItem>
                  <SelectItem value="1">Primeros Auxilios Básicos</SelectItem>
                  <SelectItem value="2">Seguridad Industrial</SelectItem>
                  <SelectItem value="3">
                    Manejo de Materiales Peligrosos
                  </SelectItem>
                  <SelectItem value="4">Prevención de Riesgos</SelectItem>
                  <SelectItem value="5">Ergonomía Laboral</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Instructor Filter */}
            <div className="space-y-2">
              <Label>Instructor</Label>
              <Select
                value={filters.instructorId || "all"}
                onValueChange={(value) =>
                  handleFilterChange("instructorId", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todos los instructores" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los instructores</SelectItem>
                  <SelectItem value="1">Dr. María González</SelectItem>
                  <SelectItem value="2">Ing. Carlos Rodríguez</SelectItem>
                  <SelectItem value="3">Dra. Ana Martínez</SelectItem>
                  <SelectItem value="4">Prof. Luis Hernández</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between mt-6 pt-4 border-t">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onFiltersChange({})}
                disabled={isLoading}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Restablecer
              </Button>
            </div>

            {onExport && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onExport("pdf")}
                  disabled={isLoading}
                >
                  <Download className="h-4 w-4 mr-1" />
                  PDF
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onExport("excel")}
                  disabled={isLoading}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Excel
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
}
