/**
 * Certificate Repository
 *
 * This file implements the repository pattern for Certificate entities.
 * It provides domain-specific methods for certificate management operations
 * while abstracting away the underlying database implementation.
 */

import type { Certificate, QueryFilter } from "../adapters/database/types";
import { BaseRepository } from "./base-repository";

// ============================================================================
// Certificate Repository Interface
// ============================================================================

export interface CertificateRepositoryInterface {
  // Certificate-specific operations
  findByUser(userId: string): Promise<Certificate[]>;
  findByCourse(courseId: string): Promise<Certificate[]>;
  findByCertificateNumber(
    certificateNumber: string,
  ): Promise<Certificate | null>;
  findByQRCode(qrCode: string): Promise<Certificate | null>;

  // Status operations
  findActiveCertificates(): Promise<Certificate[]>;
  findExpiredCertificates(): Promise<Certificate[]>;
  findRevokedCertificates(): Promise<Certificate[]>;

  // Certificate lifecycle
  issueCertificate(certificateData: Partial<Certificate>): Promise<Certificate>;
  revokeCertificate(
    certificateId: string,
    reason?: string,
  ): Promise<Certificate | null>;
  renewCertificate(
    certificateId: string,
    newExpiryDate: string,
  ): Promise<Certificate | null>;

  // Verification
  verifyCertificate(certificateNumber: string): Promise<{
    valid: boolean;
    certificate?: Certificate;
    reason?: string;
  }>;

  // Search and filtering
  findCertificatesWithDetails(): Promise<unknown[]>;
  findCertificatesByDateRange(
    startDate: string,
    endDate: string,
  ): Promise<Certificate[]>;

  // Statistics
  getCertificateStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    revoked: number;
    byStatus: Record<string, number>;
  }>;
}

// ============================================================================
// Certificate Repository Implementation
// ============================================================================

export class CertificateRepository
  extends BaseRepository<Certificate>
  implements CertificateRepositoryInterface
{
  protected tableName = "certificates";

  // ============================================================================
  // Certificate-specific Find Operations
  // ============================================================================

  async findByUser(userId: string): Promise<Certificate[]> {
    return this.findMany([this.userFilter(userId)], {
      orderBy: [{ column: "issue_date", ascending: false }],
    });
  }

  async findByCourse(courseId: string): Promise<Certificate[]> {
    return this.findMany(
      [{ column: "course_id", operator: "eq", value: courseId }],
      {
        orderBy: [{ column: "issue_date", ascending: false }],
      },
    );
  }

  async findByCertificateNumber(
    certificateNumber: string,
  ): Promise<Certificate | null> {
    return this.findOne([
      {
        column: "certificate_number",
        operator: "eq",
        value: certificateNumber,
      },
    ]);
  }

  async findByQRCode(qrCode: string): Promise<Certificate | null> {
    return this.findOne([{ column: "qr_code", operator: "eq", value: qrCode }]);
  }

  // ============================================================================
  // Status-based Operations
  // ============================================================================

  async findActiveCertificates(): Promise<Certificate[]> {
    return this.findMany(
      [{ column: "status", operator: "eq", value: "active" }],
      {
        orderBy: [{ column: "issue_date", ascending: false }],
      },
    );
  }

  async findExpiredCertificates(): Promise<Certificate[]> {
    return this.findMany(
      [{ column: "status", operator: "eq", value: "expired" }],
      {
        orderBy: [{ column: "expiry_date", ascending: false }],
      },
    );
  }

  async findRevokedCertificates(): Promise<Certificate[]> {
    return this.findMany(
      [{ column: "status", operator: "eq", value: "revoked" }],
      {
        orderBy: [{ column: "updated_at", ascending: false }],
      },
    );
  }

  // ============================================================================
  // Certificate Lifecycle Operations
  // ============================================================================

  async issueCertificate(
    certificateData: Partial<Certificate>,
  ): Promise<Certificate> {
    // Generate certificate number if not provided
    const certificateNumber =
      certificateData.certificate_number ||
      (await this.generateCertificateNumber());

    const certificateToCreate = {
      ...certificateData,
      certificate_number: certificateNumber,
      status: "active" as const,
      issue_date: certificateData.issue_date || new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    return this.create(certificateToCreate);
  }

  async revokeCertificate(
    certificateId: string,
    _reason?: string,
  ): Promise<Certificate | null> {
    return this.update(certificateId, {
      status: "revoked",
      updated_at: new Date().toISOString(),
      // Note: In a real implementation, you might want to add a revocation_reason field
    } as Partial<Certificate>);
  }

  async renewCertificate(
    certificateId: string,
    newExpiryDate: string,
  ): Promise<Certificate | null> {
    return this.update(certificateId, {
      expiry_date: newExpiryDate,
      status: "active",
      updated_at: new Date().toISOString(),
    } as Partial<Certificate>);
  }

  // ============================================================================
  // Verification Operations
  // ============================================================================

  async verifyCertificate(certificateNumber: string): Promise<{
    valid: boolean;
    certificate?: Certificate;
    reason?: string;
  }> {
    try {
      const certificate = await this.findByCertificateNumber(certificateNumber);

      if (!certificate) {
        return {
          valid: false,
          reason: "Certificate not found",
        };
      }

      if (certificate.status === "revoked") {
        return {
          valid: false,
          certificate,
          reason: "Certificate has been revoked",
        };
      }

      if (certificate.status === "expired") {
        return {
          valid: false,
          certificate,
          reason: "Certificate has expired",
        };
      }

      // Check if certificate has an expiry date and if it's past due
      if (certificate.expiry_date) {
        const expiryDate = new Date(certificate.expiry_date);
        const now = new Date();

        if (expiryDate < now) {
          // Update status to expired
          await this.update(certificate.id, {
            status: "expired",
            updated_at: new Date().toISOString(),
          } as Partial<Certificate>);

          return {
            valid: false,
            certificate: { ...certificate, status: "expired" },
            reason: "Certificate has expired",
          };
        }
      }

      return {
        valid: true,
        certificate,
      };
    } catch (error) {
      console.error("Error verifying certificate:", error);
      return {
        valid: false,
        reason: "Error verifying certificate",
      };
    }
  }

  // ============================================================================
  // Search and Filtering Operations
  // ============================================================================

  async findCertificatesWithDetails(): Promise<unknown[]> {
    try {
      // Use RPC for complex joins with user and course information
      return await this.rpc("get_certificates_with_details");
    } catch (error) {
      console.error("Error finding certificates with details:", error);
      // Fallback to basic certificate query
      return this.findAll();
    }
  }

  async findCertificatesByDateRange(
    startDate: string,
    endDate: string,
  ): Promise<Certificate[]> {
    return this.findMany(
      [
        { column: "issue_date", operator: "gte", value: startDate },
        { column: "issue_date", operator: "lte", value: endDate },
      ],
      {
        orderBy: [{ column: "issue_date", ascending: false }],
      },
    );
  }

  // ============================================================================
  // Statistics and Analytics
  // ============================================================================

  async getCertificateStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    revoked: number;
    byStatus: Record<string, number>;
  }> {
    try {
      const [total, active, expired, revoked] = await Promise.all([
        this.count(),
        this.count([{ column: "status", operator: "eq", value: "active" }]),
        this.count([{ column: "status", operator: "eq", value: "expired" }]),
        this.count([{ column: "status", operator: "eq", value: "revoked" }]),
      ]);

      return {
        total,
        active,
        expired,
        revoked,
        byStatus: {
          active,
          expired,
          revoked,
        },
      };
    } catch (error) {
      console.error("Error getting certificate stats:", error);
      throw error;
    }
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private async generateCertificateNumber(): Promise<string> {
    // Generate a unique certificate number
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");
    const certificateNumber = `CERT-${timestamp}-${random}`;

    // Ensure uniqueness
    const existing = await this.findByCertificateNumber(certificateNumber);
    if (existing) {
      // Recursive call if collision (very unlikely)
      return this.generateCertificateNumber();
    }

    return certificateNumber;
  }

  // ============================================================================
  // Validation Helpers
  // ============================================================================

  async validateUniqueCertificateNumber(
    certificateNumber: string,
    excludeCertificateId?: string,
  ): Promise<boolean> {
    const filters: QueryFilter[] = [
      {
        column: "certificate_number",
        operator: "eq",
        value: certificateNumber,
      },
    ];

    if (excludeCertificateId) {
      filters.push({
        column: "id",
        operator: "neq",
        value: excludeCertificateId,
      });
    }

    const existingCertificate = await this.findOne(filters);
    return existingCertificate === null;
  }
}
