/**
 * @fileoverview Dashboard Breadcrumbs Component
 *
 * Componente de navegación breadcrumb para dashboards de QR CURSE.
 * Proporciona navegación jerárquica y contexto de ubicación.
 */

"use client";

import { ChevronRight, Home } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface DashboardBreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
  separator?: React.ComponentType<{ className?: string }>;
}

/**
 * Componente de breadcrumbs para navegación jerárquica
 */
export function DashboardBreadcrumbs({
  items,
  className,
  showHome = true,
  separator: Separator = ChevronRight,
}: DashboardBreadcrumbsProps) {
  // Agregar elemento "Home" al inicio si está habilitado
  const breadcrumbItems = showHome
    ? [{ label: "Dashboard", href: "/dashboard", icon: Home }, ...items]
    : items;

  return (
    <nav
      aria-label="Breadcrumb"
      className={cn(
        "flex items-center space-x-1 text-sm text-muted-foreground",
        className,
      )}
    >
      <ol className="flex items-center space-x-1">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          const Icon = item.icon;

          return (
            <li key={item.href || item.label} className="flex items-center">
              {/* Separador (excepto para el primer elemento) */}
              {index > 0 && (
                <Separator className="h-4 w-4 mx-2 text-muted-foreground/50" />
              )}

              {/* Elemento del breadcrumb */}
              <div className="flex items-center">
                {Icon && <Icon className="h-4 w-4 mr-1" />}

                {isLast ? (
                  // Último elemento (página actual) - no es clickeable
                  <span
                    className="font-medium text-foreground"
                    aria-current="page"
                  >
                    {item.label}
                  </span>
                ) : (
                  // Elementos anteriores - clickeables
                  <Link
                    href={item.href || "#"}
                    className="hover:text-foreground transition-colors"
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

/**
 * Breadcrumbs compactos para espacios reducidos
 */
export function CompactBreadcrumbs({
  items,
  className,
}: {
  items: BreadcrumbItem[];
  className?: string;
}) {
  if (items.length === 0) return null;

  const lastItem = items[items.length - 1];
  const hasParent = items.length > 1;
  const parentItem = hasParent ? items[items.length - 2] : null;

  return (
    <nav
      aria-label="Breadcrumb"
      className={cn(
        "flex items-center space-x-1 text-sm text-muted-foreground",
        className,
      )}
    >
      {hasParent && parentItem && (
        <>
          <Link
            href={parentItem.href || "#"}
            className="hover:text-foreground transition-colors truncate max-w-[120px]"
          >
            {parentItem.label}
          </Link>
          <ChevronRight className="h-4 w-4 text-muted-foreground/50" />
        </>
      )}
      <span className="font-medium text-foreground truncate">
        {lastItem.label}
      </span>
    </nav>
  );
}

/**
 * Hook para generar breadcrumbs automáticamente basado en la ruta
 */
export function useAutoBreadcrumbs(pathname: string) {
  const segments = pathname.split("/").filter(Boolean);

  const breadcrumbs: BreadcrumbItem[] = segments.map((segment, index) => {
    const href = `/${segments.slice(0, index + 1).join("/")}`;
    const label = formatSegmentLabel(segment);

    return {
      label,
      href: index === segments.length - 1 ? undefined : href, // Último elemento sin href
    };
  });

  return breadcrumbs;
}

/**
 * Formatea un segmento de URL para mostrar como label
 */
function formatSegmentLabel(segment: string): string {
  // Mapeo de segmentos conocidos
  const segmentMap: Record<string, string> = {
    dashboard: "Dashboard",
    certificates: "Certificados",
    users: "Usuarios",
    courses: "Cursos",
    companies: "Empresas",
    reports: "Reportes",
    settings: "Configuración",
    profile: "Perfil",
    help: "Ayuda",
    templates: "Plantillas",
    create: "Crear",
    edit: "Editar",
    view: "Ver",
    new: "Nuevo",
  };

  // Si existe un mapeo específico, usarlo
  if (segmentMap[segment]) {
    return segmentMap[segment];
  }

  // Si es un UUID o ID, mostrar como "ID"
  if (
    segment.match(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
    )
  ) {
    return "Detalles";
  }

  // Formatear el segmento: reemplazar guiones con espacios y capitalizar
  return segment
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

/**
 * Breadcrumbs con dropdown para rutas largas
 */
export function DropdownBreadcrumbs({
  items,
  maxVisible = 3,
  className,
}: {
  items: BreadcrumbItem[];
  maxVisible?: number;
  className?: string;
}) {
  if (items.length <= maxVisible) {
    return <DashboardBreadcrumbs items={items} className={className} />;
  }

  const firstItem = items[0];
  const lastItems = items.slice(-maxVisible + 1);
  const hiddenItems = items.slice(1, -maxVisible + 1);

  return (
    <nav
      aria-label="Breadcrumb"
      className={cn(
        "flex items-center space-x-1 text-sm text-muted-foreground",
        className,
      )}
    >
      <ol className="flex items-center space-x-1">
        {/* Primer elemento */}
        <li className="flex items-center">
          {firstItem.icon && <firstItem.icon className="h-4 w-4 mr-1" />}
          <Link
            href={firstItem.href || "#"}
            className="hover:text-foreground transition-colors"
          >
            {firstItem.label}
          </Link>
        </li>

        {/* Separador */}
        <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground/50" />

        {/* Dropdown para elementos ocultos */}
        {hiddenItems.length > 0 && (
          <li className="flex items-center">
            <button
              type="button"
              className="hover:text-foreground transition-colors px-2 py-1 rounded"
              title={`${hiddenItems.length} elementos ocultos`}
            >
              ...
            </button>
            <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground/50" />
          </li>
        )}

        {/* Últimos elementos */}
        {lastItems.map((item, index) => {
          const isLast = index === lastItems.length - 1;
          const Icon = item.icon;

          return (
            <li key={item.href || item.label} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground/50" />
              )}

              <div className="flex items-center">
                {Icon && <Icon className="h-4 w-4 mr-1" />}

                {isLast ? (
                  <span
                    className="font-medium text-foreground"
                    aria-current="page"
                  >
                    {item.label}
                  </span>
                ) : (
                  <Link
                    href={item.href || "#"}
                    className="hover:text-foreground transition-colors"
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            </li>
          );
        })}
      </ol>
    </nav>
  );
}
