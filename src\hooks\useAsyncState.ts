"use client";

import { useCallback, useEffect, useRef, useState } from "react";

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  success: boolean;
}

export interface AsyncActions<T> {
  execute: (...args: unknown[]) => Promise<T>;
  reset: () => void;
  setData: (data: T) => void;
  setError: (error: Error) => void;
}

export interface UseAsyncStateOptions {
  immediate?: boolean;
  onSuccess?: (data: unknown) => void;
  onError?: (error: Error) => void;
  retryCount?: number;
  retryDelay?: number;
}

/**
 * Hook para manejar estados asíncronos con loading, error y success
 */
export function useAsyncState<T = unknown>(
  asyncFunction: (...args: unknown[]) => Promise<T>,
  options: UseAsyncStateOptions = {},
): [AsyncState<T>, AsyncActions<T>] {
  const {
    immediate = false,
    onSuccess,
    onError,
    retryCount = 0,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  });

  const mountedRef = useRef(true);
  const retryCountRef = useRef(0);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const execute = useCallback(
    async (...args: unknown[]): Promise<T> => {
      if (!mountedRef.current) {
        return Promise.resolve(null as T);
      }

      setState((prev) => ({
        ...prev,
        loading: true,
        error: null,
        success: false,
      }));

      try {
        const result = await asyncFunction(...args);

        if (!mountedRef.current) {
          return result;
        }

        setState({
          data: result,
          loading: false,
          error: null,
          success: true,
        });

        retryCountRef.current = 0;
        onSuccess?.(result);
        return result;
      } catch (error: unknown) {
        if (!mountedRef.current) {
          return Promise.resolve(null as T);
        }

        const errorObj =
          error instanceof Error ? error : new Error(String(error));

        // Retry logic
        if (retryCountRef.current < retryCount) {
          retryCountRef.current++;

          setTimeout(() => {
            if (mountedRef.current) {
              execute(...args);
            }
          }, retryDelay);

          return Promise.resolve(null as T);
        }

        setState({
          data: null,
          loading: false,
          error: errorObj,
          success: false,
        });

        onError?.(errorObj);
        return Promise.resolve(null as T);
      }
    },
    [asyncFunction, onSuccess, onError, retryCount, retryDelay],
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    });
    retryCountRef.current = 0;
  }, []);

  const setData = useCallback((data: T) => {
    setState((prev) => ({
      ...prev,
      data,
      success: true,
      error: null,
    }));
  }, []);

  const setError = useCallback((error: Error) => {
    setState((prev) => ({
      ...prev,
      error,
      success: false,
      loading: false,
    }));
  }, []);

  // Execute immediately if requested
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute]);

  return [
    state,
    {
      execute,
      reset,
      setData,
      setError,
    },
  ];
}

/**
 * Hook simplificado para operaciones CRUD
 */
export function useCrudState<T = unknown>() {
  const [createState, createActions] = useAsyncState<T>(async () => {
    throw new Error("Create function not implemented");
  });

  const [readState, readActions] = useAsyncState<T[]>(async () => {
    throw new Error("Read function not implemented");
  });

  const [updateState, updateActions] = useAsyncState<T>(async () => {
    throw new Error("Update function not implemented");
  });

  const [deleteState, deleteActions] = useAsyncState<boolean>(async () => {
    throw new Error("Delete function not implemented");
  });

  const isLoading =
    createState.loading ||
    readState.loading ||
    updateState.loading ||
    deleteState.loading;
  const hasError =
    createState.error ||
    readState.error ||
    updateState.error ||
    deleteState.error;

  return {
    create: { state: createState, actions: createActions },
    read: { state: readState, actions: readActions },
    update: { state: updateState, actions: updateActions },
    delete: { state: deleteState, actions: deleteActions },
    isLoading,
    hasError,
  };
}

/**
 * NOTA IMPORTANTE:
 * Por reglas de React, los hooks deben llamarse siempre en el mismo orden y número en cada render,
 * y nunca dentro de bucles, funciones, condiciones o callbacks dinámicos.
 *
 * Por eso, se eliminó el hook useMultipleAsync. Para manejar múltiples operaciones asíncronas,
 * llama a useAsyncState explícitamente para cada operación en el componente o custom hook.
 *
 * Ejemplo recomendado:
 *
 *   const [userState, userActions] = useAsyncState(fetchUser);
 *   const [courseState, courseActions] = useAsyncState(fetchCourse);
 *
 * Así se garantiza el cumplimiento de las reglas de hooks y la estabilidad del estado de React.
 */
