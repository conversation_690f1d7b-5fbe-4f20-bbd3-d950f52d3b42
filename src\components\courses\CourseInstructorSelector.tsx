"use client";

import { UserPlus } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import { supabase } from "@/lib/supabase";

interface CourseInstructorSelectorProps {
  courseId: string;
  currentInstructorId: string | null;
  onInstructorChange: (instructorId: string) => void;
  instructors: Instructor[];
  isLoading: boolean;
  isDisabled: boolean;
  showCreateOption: boolean;
  onCreateInstructor: () => void;
}

interface Instructor {
  id: string;
  name: string;
  email: string | null;
  signature_url: string | null;
}

export default function CourseInstructorSelector({
  currentInstructorId,
  onInstructorChange,
  instructors,
}: CourseInstructorSelectorProps) {
  const [loading, setLoading] = useState<boolean>(true);
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [mounted, setMounted] = useState<boolean>(false);

  // Estado para el formulario de nuevo instructor
  const [newInstructorName, setNewInstructorName] = useState<string>("");
  const [newInstructorEmail, setNewInstructorEmail] = useState<string>("");
  const [newInstructorPhone, setNewInstructorPhone] = useState<string>("");
  const [isCreatingInstructor, setIsCreatingInstructor] =
    useState<boolean>(false);
  const [formErrors, setFormErrors] = useState<{
    name?: string;
    email?: string;
    phone?: string;
  }>({});

  // Asegurarse de que el componente solo se renderice en el cliente
  useEffect(() => {
    setMounted(true);
  }, []);

  // Cargar instructores
  useEffect(() => {
    async function loadInstructors() {
      setLoading(true);
      try {
        // Cargar todos los instructores desde la tabla instructors
        const { error: instructorsError } = await supabase
          .from("instructors")
          .select("id, name, email, signature_url")
          .order("name", { ascending: true });

        if (instructorsError) {
          console.error("Error al cargar instructores:", instructorsError);
          throw instructorsError;
        }

        // Si estamos creando un nuevo curso y no hay instructor seleccionado,
        // no seleccionamos automáticamente ningún instructor
      } catch (error) {
        const errMsg =
          error instanceof Error
            ? error.message
            : "Error cargando instructores.";
        console.error("Error loading instructors:", error);
        toast({
          title: "Error al cargar instructores",
          description: errMsg,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }

    loadInstructors();
  }, []);

  // Validar formulario de nuevo instructor
  const validateNewInstructorForm = () => {
    const errors: {
      name?: string;
      email?: string;
      phone?: string;
    } = {};
    let isValid = true;

    if (!newInstructorName.trim()) {
      errors.name = "El nombre es obligatorio";
      isValid = false;
    }

    if (
      newInstructorEmail &&
      !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newInstructorEmail)
    ) {
      errors.email = "El correo electrónico no es válido";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Crear nuevo instructor
  const handleCreateInstructor = async () => {
    if (!validateNewInstructorForm()) return;

    setIsCreatingInstructor(true);
    try {
      const newInstructor = {
        name: newInstructorName,
        email: newInstructorEmail || null,
        phone: newInstructorPhone || null,
      };

      const { data, error } = await supabase
        .from("instructors")
        .insert([newInstructor])
        .select()
        .single();

      if (error) throw error;

      // Actualizar la lista de instructores
      // setInstructors([...instructors, data]); // Eliminar: redeclaración de instructors

      // Seleccionar automáticamente al nuevo instructor
      onInstructorChange(data.id);

      // Resetear formulario
      setNewInstructorName("");
      setNewInstructorEmail("");
      setNewInstructorPhone("");
      setFormErrors({});

      // Cerrar el diálogo
      setIsDialogOpen(false);

      toast({
        title: "Instructor creado",
        description: "El instructor se ha creado correctamente",
      });
    } catch (error) {
      const errMsg =
        error instanceof Error ? error.message : "Error creando instructor.";
      console.error("Error creating instructor:", error);
      toast({
        title: "Error al crear instructor",
        description: errMsg,
        variant: "destructive",
      });
    } finally {
      setIsCreatingInstructor(false);
    }
  };

  // Si no está montado, devolvemos un div vacío para evitar errores de hidratación
  if (!mounted) {
    return (
      <div className="h-9 flex items-center px-3 border rounded-md bg-gray-50">
        <span className="text-sm text-gray-500">Cargando...</span>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-end gap-2">
        <div className="flex-1">
          {loading ? (
            <div className="h-9 flex items-center px-3 border rounded-md bg-gray-50">
              <span className="text-sm text-gray-500">
                Cargando instructores...
              </span>
            </div>
          ) : (
            <Select
              value={currentInstructorId || "none"}
              onValueChange={onInstructorChange}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar instructor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Sin instructor asignado</SelectItem>
                {instructors.map((instructor) => (
                  <SelectItem key={instructor.id} value={instructor.id}>
                    {instructor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              type="button"
              size="sm"
              variant="outline"
              className="flex items-center gap-1"
              disabled={loading}
            >
              <UserPlus className="h-4 w-4" />
              Nuevo
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Nuevo Instructor</DialogTitle>
              <DialogDescription>
                Ingresa los datos del nuevo instructor para agregarlo al
                sistema.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nombre completo *</Label>
                <Input
                  id="name"
                  value={newInstructorName}
                  onChange={(e) => setNewInstructorName(e.target.value)}
                  placeholder="Ej: Juan Pérez"
                />
                {formErrors.name && (
                  <p className="text-sm text-red-500">{formErrors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Correo electrónico</Label>
                <Input
                  id="email"
                  type="email"
                  value={newInstructorEmail}
                  onChange={(e) => setNewInstructorEmail(e.target.value)}
                  placeholder="Ej: <EMAIL>"
                />
                {formErrors.email && (
                  <p className="text-sm text-red-500">{formErrors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Teléfono</Label>
                <Input
                  id="phone"
                  value={newInstructorPhone}
                  onChange={(e) => setNewInstructorPhone(e.target.value)}
                  placeholder="Ej: +56912345678"
                />
                {formErrors.phone && (
                  <p className="text-sm text-red-500">{formErrors.phone}</p>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                onClick={handleCreateInstructor}
                disabled={isCreatingInstructor}
              >
                {isCreatingInstructor ? "Creando..." : "Crear Instructor"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
