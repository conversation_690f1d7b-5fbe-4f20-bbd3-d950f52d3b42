# Prompt para Agente de Código: Corrección de Errores y Advertencias

## Objetivo
Resuelve **todos los errores y advertencias** reportados por Biome, TypeScript y el linter en este proyecto. Aplica las mejores prácticas de accesibilidad, tipado, limpieza de props/variables, y compatibilidad cross-browser. Relaciona cada error con su archivo y línea para trazabilidad.

---

## Instrucciones para el Agente

1. **Analiza el reporte de Biome y el linter.**
   - Identifica todos los errores y advertencias activos.
   - Relaciona cada uno con su archivo y línea (ejemplo: `src/app/cursos/[id]/page.tsx:571`).

2. **Corrige los siguientes tipos de problemas:**
   - SVGs sin `<title>` accesible (`noSvgWithoutTitle`).
   - Uso de `any` en bloques `catch` o estados (`noExplicitAny`).
   - Props, variables y parámetros no usados (`noUnusedVariables`, `noUnusedFunctionParameters`).
   - Uso de `document.cookie` (agrega comentarios de compatibilidad y migración futura a Cookie Store API).
   - Hooks como dependencias sin `useCallback` (`useExhaustiveDependencies`).
   - Cualquier advertencia de accesibilidad (`a11y`).
   - Limpieza de suppressions de Biome sin efecto.

3. **Para cada corrección:**
   - Aplica la solución mínima y robusta.
   - Deja un comentario explicativo si la solución requiere justificación (ejemplo: compatibilidad cross-browser).
   - Si la advertencia es por falta de contexto (ejemplo: `<title>` vacío), agrega un texto relevante en español.

4. **Ejemplo de formato de corrección:**
   - `src/app/cursos/[id]/page.tsx:571` — Agregar `<title>Icono de información</title>` al SVG.
   - `src/components/courses/CourseInstructorSelector.tsx:49` — Eliminar parámetro no usado `isLoading`.
   - `src/lib/supabase.ts:36` — Agregar comentario biome-ignore y justificación en el uso de `document.cookie`.

5. **Repite el proceso hasta que el proyecto esté libre de errores y advertencias.**

---

## Contexto de rutas y archivos afectados

- `src/app/cursos/[id]/page.tsx` — SVGs accesibles, títulos, props no usadas.
- `src/components/courses/CourseInstructorSelector.tsx` — Props/variables no usadas, tipado.
- `src/components/courses/CourseLegalFrameworkManager.tsx` — Variables no usadas en bloques catch.
- `src/components/courses/CourseObjectivesManager.tsx` — Tipado robusto en errores.
- `src/components/courses/CourseContentManager.tsx` — Tipado robusto en errores.
- `src/app/login/page.tsx` — Uso de cookies, comentarios biome-ignore.
- `src/lib/supabase.ts` — Uso de cookies, comentarios biome-ignore.
- `src/app/verificar-certificado/page.tsx` — Tipado en errores, hooks en dependencias.

---

## Criterios de éxito
- El proyecto debe quedar **sin errores ni advertencias** en Biome, TypeScript y el linter.
- El código debe ser accesible, robusto y fácil de mantener.
- Todas las correcciones deben estar documentadas y justificadas si es necesario.