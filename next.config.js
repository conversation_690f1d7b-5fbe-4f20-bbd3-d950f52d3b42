/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  env: {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || "https://mrgcukvyvivpsacohdqh.supabase.co",
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1yZ2N1a3Z5dml2cHNhY29oZHFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE4NTI2MDIsImV4cCI6MjA1NzQyODYwMn0.UHN1JujAjGStmJ37gVRKgRoAwhiJHShzV7RU4cy5t-4",
  },
  reactStrictMode: true,

  // Modern image configuration for Next.js 15.3.5
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'mrgcukvyvivpsacohdqh.supabase.co',
        port: '',
        pathname: '/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Disable linting during build (using Biome instead)
  eslint: {
    ignoreDuringBuilds: true,
    dirs: [],
  },

  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: true,
  },

  // SWC Compiler optimizations for Next.js 15.3.5
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
    styledComponents: true,
  },

  // Modern experimental features for Next.js 15.3.5
  experimental: {
    strictNextHead: false,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  // Turbopack configuration (stable in Next.js 15.3.5)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // Performance optimizations
  poweredByHeader: false,
  compress: true,

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
