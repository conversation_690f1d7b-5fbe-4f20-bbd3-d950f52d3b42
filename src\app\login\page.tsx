"use client";

import { AuthError } from "@supabase/supabase-js";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { type FormEvent, Suspense, useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import SessionDebugger from "./session-debugger";

// Definir la función logBrowserCookies localmente para evitar problemas de importación
const logBrowserCookies = () => {
  if (typeof document === "undefined") return;

  console.log("[Auth] Cookies disponibles en navegador:", document.cookie);

  // Buscar específicamente cookies relacionadas con Supabase
  const supabaseCookies = document.cookie
    .split("; ")
    .filter((cookie) => cookie.includes("sb-") || cookie.includes("supabase"))
    .map((cookie) => cookie.trim());

  if (supabaseCookies.length > 0) {
    console.log("[Auth] Cookies de Supabase encontradas:", supabaseCookies);
  } else {
    console.log("[Auth] No se encontraron cookies de Supabase");
  }
};

// Componente que usa useSearchParams envuelto en Suspense
function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const initialError = searchParams.get("error");
  const redirectTo = searchParams.get("redirect") || ""; // Capture redirect param

  useEffect(() => {
    if (initialError) {
      setError(decodeURIComponent(initialError));
    }

    // Log de cookies al cargar la página
    logBrowserCookies();

    // Verificar si ya hay una sesión activa
    const checkExistingSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session) {
        console.log(
          "Sesión existente detectada en carga de página:",
          session.user.id,
        );

        // Obtener información del usuario usando la nueva función
        try {
          // Usar la función RPC get_user_info que accede de forma segura a auth.users
          const { data, error } = await supabase.rpc("get_user_info");

          if (error) throw error;

          const profile = data && data.length > 0 ? data[0] : null;

          // Redirigir según rol
          const path =
            profile?.role?.toLowerCase() === "admin"
              ? "/panel-admin"
              : "/panel-alumno";
          console.log(`Redirigiendo a usuario existente a: ${path}`);

          // Mostrar mensaje
          setSuccessMessage(
            `Sesión existente detectada. Redirigiendo a ${path}...`,
          );

          // Redireccionar tras breve espera
          setTimeout(() => {
            window.location.href = path;
          }, 1500);
        } catch (error) {
          console.error("Error obteniendo perfil de sesión existente:", error);
        }
      }
    };

    checkExistingSession();
  }, [initialError]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);
    setSuccessMessage(null);

    try {
      console.log("Intentando iniciar sesión con:", email);

      // 1. Limpiar cookies existentes para evitar conflictos
      document.cookie.split(";").forEach((cookie) => {
        const [name] = cookie.split("=");
        // biome-ignore lint/suspicious/noDocumentCookie: Uso necesario para compatibilidad cross-browser. Migrar a Cookie Store API cuando sea ampliamente soportada.
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      });

      // 2. Autenticar con Supabase
      const { data, error: signInError } =
        await supabase.auth.signInWithPassword({
          email,
          password,
        });

      if (signInError) throw signInError;

      if (data?.user) {
        console.log("Login exitoso, obteniendo perfil de usuario...");
        setSuccessMessage("Login exitoso! Obteniendo información de perfil...");

        // IMPORTANTE: Esperar un poco para asegurar que las cookies se establezcan
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Verificar que la sesión realmente existe después del login
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session) {
          console.error("Sesión no disponible después de login exitoso");
          throw new Error(
            "No se pudo establecer la sesión. Por favor intenta nuevamente.",
          );
        }

        console.log("Sesión confirmada:", session.user.id);
        setSuccessMessage("Sesión confirmada! Obteniendo perfil...");

        // 3. Obtener información del usuario usando la función RPC
        const { data: userData, error: userError } =
          await supabase.rpc("get_user_info");

        if (userError) {
          console.error("Error al obtener el perfil:", userError);
          throw new Error(
            "No se pudo obtener la información de tu perfil. Por favor, intenta nuevamente.",
          );
        }

        const profile = userData && userData.length > 0 ? userData[0] : null;

        if (!profile) {
          throw new Error(
            "No se encontró información de perfil. Por favor, contacta a soporte.",
          );
        }

        console.log("Perfil obtenido:", profile);

        // 5. Decidir a dónde redirigir - todos van al panel unificado
        let redirectPath: string = "/panel";

        if (redirectTo?.startsWith("/panel")) {
          // Si hay un parámetro redirect válido, usarlo
          redirectPath = redirectTo;
          console.log(`Redirigiendo a ruta solicitada: ${redirectPath}`);
        } else {
          console.log(`Redirigiendo al panel unificado: ${redirectPath}`);
        }

        // 6. Actualizar el mensaje en la UI y programar la redirección
        setLoading(false);
        setError(null);
        setSuccessMessage(`Login exitoso! Redirigiendo a ${redirectPath}...`);

        // Esperar 1 segundo más para mostrar mensaje de éxito
        setTimeout(() => {
          // Usar window.location en lugar de router.push para forzar recarga completa
          console.log(`Ejecutando redirección a ${redirectPath}`);
          window.location.href = redirectPath;
        }, 1500);

        return; // Importante: terminamos aquí para evitar que se ejecute setLoading(false) abajo
      } else {
        throw new Error(
          "La autenticación fue exitosa pero no se recibieron datos de usuario.",
        );
      }
    } catch (error) {
      console.error("Error en página de login:", error);

      // Mejorar el manejo de errores para mostrar mensajes más claros
      if (error instanceof AuthError) {
        switch (error.message) {
          case "Invalid login credentials":
            setError(
              "Credenciales incorrectas. Verifica tu email y contraseña.",
            );
            break;
          case "Email not confirmed":
            setError(
              "Tu email no ha sido confirmado. Por favor, verifica tu bandeja de entrada.",
            );
            break;
          default:
            setError(error.message || "Error de autenticación");
        }
      } else if (error instanceof Error) {
        setError(error.message || "Error al iniciar sesión");
      } else {
        setError("Error desconocido al iniciar sesión");
      }

      setLoading(false);
      setSuccessMessage(null);
    }
  };

  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <SessionDebugger />

      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Inicia sesión en tu cuenta
        </h2>
      </div>

      {successMessage && (
        <div className="fixed top-0 left-0 right-0 bg-green-500 text-white p-4 text-center font-bold">
          {successMessage}
        </div>
      )}

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                Correo electrónico
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                Contraseña
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {loading ? "Iniciando sesión..." : "Iniciar sesión"}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  ¿No tienes una cuenta?
                </span>
              </div>
            </div>

            <div className="mt-6">
              <Link
                href="/register"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Registrarse
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente principal que envuelve LoginForm en Suspense
export default function Login() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          Cargando...
        </div>
      }
    >
      <LoginForm />
    </Suspense>
  );
}
