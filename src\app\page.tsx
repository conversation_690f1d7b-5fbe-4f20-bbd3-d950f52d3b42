"use client";
import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      {/* Hero Section */}
      <section className="hero-gradient relative overflow-hidden flex flex-col justify-center items-center py-20 md:py-32 animate-slide-up">
        <div className="absolute inset-0 subtle-pattern"></div>
        <div className="relative z-10 w-full flex flex-col items-center">
          {/* Logo opcional, puedes reemplazar o eliminar la imagen */}
          <div className="flex justify-center mb-8">
            <div className="bg-white rounded-full p-4 shadow-xl">
              <Image
                src="/images/logo1.svg"
                alt="Logo Empresa"
                width={120}
                height={120}
                className="w-28 h-auto"
                priority
              />
            </div>
          </div>
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-heading font-black text-white drop-shadow-lg text-center mb-4 animate-slide-up">
            Plataforma Online de Cursos y Certificación
          </h1>
          <p className="text-xl md:text-2xl lg:text-3xl text-white/90 max-w-3xl mx-auto font-body font-semibold text-center mb-6 animate-slide-up">
            Lanza, gestiona y certifica a tu equipo o clientes con una plataforma moderna, robusta y lista para personalizar para cualquier empresa.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center pt-4 animate-slide-up">
            <a
              href="/login"
              className="btn-gradient inline-flex items-center justify-center rounded-lg px-8 py-4 text-lg font-heading font-bold text-white shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl"
            >
              Iniciar Sesión
            </a>
            <a
              href="/register"
              className="inline-flex items-center justify-center rounded-lg border-2 border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-lg font-heading font-bold text-white shadow-xl transition-all duration-300 hover:bg-white/20 hover:scale-105"
            >
              Registrarse
            </a>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 lg:py-24 xl:py-32 bg-background animate-slide-up">
        <div className="container mx-auto px-4 lg:px-6 xl:px-8">
          <div className="text-center mb-12 lg:mb-16 xl:mb-20">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-heading font-bold text-primary mb-4 lg:mb-6">
              ¿Qué ofrece esta plataforma?
            </h2>
            <p className="text-base lg:text-lg xl:text-xl text-muted-foreground max-w-4xl mx-auto font-body">
              Una solución completa para la gestión educativa y certificación digital adaptable a cualquier organización.
            </p>
          </div>

          <div className="w-full max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="text-center p-8 rounded-xl bg-card border border-border shadow-lg hover:shadow-xl transition-all duration-300 card-hover animate-slide-up">
                <div className="text-5xl mb-6">⚡</div>
                <h3 className="text-xl font-bold text-card-foreground mb-4">Implementación Rápida</h3>
                <p className="text-muted-foreground">Personalizable y lista para usar en minutos</p>
              </div>
              <div className="text-center p-8 rounded-xl bg-card border border-border shadow-lg hover:shadow-xl transition-all duration-300 card-hover animate-slide-up">
                <div className="text-5xl mb-6">🔐</div>
                <h3 className="text-xl font-bold text-card-foreground mb-4">Seguridad Avanzada</h3>
                <p className="text-muted-foreground">Control de roles y autenticación robusta</p>
              </div>
              <div className="text-center p-8 rounded-xl bg-card border border-border shadow-lg hover:shadow-xl transition-all duration-300 card-hover animate-slide-up">
                <div className="text-5xl mb-6">📄</div>
                <h3 className="text-xl font-bold text-card-foreground mb-4">Certificados QR</h3>
                <p className="text-muted-foreground">Certificados digitales verificables</p>
              </div>
              <div className="text-center p-8 rounded-xl bg-card border border-border shadow-lg hover:shadow-xl transition-all duration-300 card-hover animate-slide-up">
                <div className="text-5xl mb-6">🧑‍💻</div>
                <h3 className="text-xl font-bold text-card-foreground mb-4">Paneles Diferenciados</h3>
                <p className="text-muted-foreground">Para administradores, instructores y estudiantes</p>
              </div>
              <div className="text-center p-8 rounded-xl bg-card border border-border shadow-lg hover:shadow-xl transition-all duration-300 card-hover animate-slide-up">
                <div className="text-5xl mb-6">🤖</div>
                <h3 className="text-xl font-bold text-card-foreground mb-4">Compatible con AI</h3>
                <p className="text-muted-foreground">Automatización y agentes inteligentes</p>
              </div>
              <div className="text-center p-8 rounded-xl bg-card border border-border shadow-lg hover:shadow-xl transition-all duration-300 card-hover animate-slide-up">
                <div className="text-5xl mb-6">🎨</div>
                <h3 className="text-xl font-bold text-card-foreground mb-4">Fácil Personalización</h3>
                <p className="text-muted-foreground">Colores, logos y textos adaptables</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 lg:py-20 xl:py-24 bg-muted animate-slide-up">
        <div className="container mx-auto px-4 lg:px-6 xl:px-8 text-center">
          <h3 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-heading font-bold text-foreground mb-4 lg:mb-6">
            ¿Listo para comenzar?
          </h3>
          <p className="text-base lg:text-lg xl:text-xl text-muted-foreground mb-6 lg:mb-8 max-w-3xl mx-auto font-body">
            ¡Forkea, personaliza y despliega tu propia plataforma de cursos en minutos!
          </p>
          <a
            href="/register"
            className="btn-gradient inline-flex items-center justify-center rounded-lg px-6 lg:px-8 py-3 lg:py-4 text-base lg:text-lg font-heading font-bold text-white shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl"
          >
            Comenzar Ahora
          </a>
        </div>
      </section>

      {/* Footer */}
      <footer className="w-full bg-background border-t border-border py-8 mt-auto animate-slide-up">
        <div className="container mx-auto px-4 flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <Image src="/images/logo1.svg" alt="Logo Empresa" width={40} height={40} className="w-10 h-10" />
            <span className="font-heading font-bold text-lg text-foreground">Nombre Empresa</span>
          </div>
          <nav className="flex gap-6 text-muted-foreground text-sm">
            <a href="/cursos" className="hover:text-primary transition">Cursos</a>
            <a href="/verificar-certificado" className="hover:text-primary transition">Verificar Certificado</a>
            <a href="/login" className="hover:text-primary transition">Iniciar Sesión</a>
            <a href="/register" className="hover:text-primary transition">Registrarse</a>
          </nav>
          <div className="text-muted-foreground text-xs">&copy; {new Date().getFullYear()} Nombre Empresa. Todos los derechos reservados.</div>
        </div>
      </footer>
    </div>
  );
}
