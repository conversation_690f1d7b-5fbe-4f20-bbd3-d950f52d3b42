"use client";

import { <PERSON><PERSON><PERSON>ir<PERSON>, CheckCircle, FileUp, X } from "lucide-react";
import { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/use-toast";

interface ImportUsersButtonProps {
  onImportStart?: () => void;
  onImportComplete?: (results: ImportResults) => void;
  onImportError?: (error: string) => void;
}

interface ImportResults {
  success: boolean;
  users: UserImportData[];
  total: number;
  valid: number;
  invalid: number;
  duplicate: number;
}

interface UserImportData {
  email: string;
  firstName: string;
  lastName: string;
  identityDocument: string;
  status: "valid" | "invalid" | "duplicate";
  errors?: string[];
}

export function ImportUsersButton({
  onImportStart,
  onImportComplete,
  onImportError,
}: ImportUsersButtonProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    const fileType = file.name.split(".").pop()?.toLowerCase();
    if (fileType !== "csv" && fileType !== "xlsx" && fileType !== "xls") {
      setUploadError(
        "Formato de archivo no soportado. Por favor, sube un archivo CSV o Excel (.xlsx, .xls).",
      );
      return;
    }

    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setUploadError(
        "El archivo es demasiado grande. El tamaño máximo permitido es 5MB.",
      );
      return;
    }

    setIsUploading(true);
    setUploadError(null);
    setUploadProgress(0);
    setShowModal(true);

    if (onImportStart) onImportStart();

    try {
      // Create FormData
      const formData = new FormData();
      formData.append("file", file);

      // Upload file
      const response = await fetch("/api/admin/import-users", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Error al importar usuarios");
      }

      const result = await response.json();

      setUploadProgress(100);

      if (onImportComplete) onImportComplete(result);

      toast({
        title: "Importación exitosa",
        description: `Se importaron ${result.imported} usuarios correctamente.`,
      });

      // Close modal after 2 seconds
      setTimeout(() => {
        setShowModal(false);
        setIsUploading(false);
      }, 2000);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Error al importar usuarios";
      setUploadError(errorMessage);
      setUploadProgress(0);

      if (onImportError) onImportError(errorMessage);

      toast({
        title: "Error de importación",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const closeModal = () => {
    setShowModal(false);
    setIsUploading(false);
    setUploadProgress(0);
    setUploadError(null);
    // Reset file input
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  return (
    <>
      <div className="relative group">
        <Button
          onClick={handleButtonClick}
          variant="outline"
          className="gap-2 bg-white hover:bg-gray-50"
          disabled={isUploading}
        >
          <FileUp className="h-4 w-4" />
          Importar Alumnos
        </Button>

        <div className="absolute right-0 mt-2 w-60 bg-white rounded-md shadow-lg hidden group-hover:block z-10">
          <div className="py-1">
            <a
              href="/templates/plantilla_importacion_alumnos.csv"
              download
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <span className="flex items-center">
                <svg
                  className="w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Icono de descarga de plantilla</title>
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                Descargar plantilla CSV
              </span>
            </a>
            <div className="border-t border-gray-100"></div>
            <div className="px-4 py-2 text-xs text-gray-500">
              Formatos aceptados: CSV, Excel (.xlsx, .xls)
            </div>
          </div>
        </div>
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".csv,.xlsx,.xls"
        className="hidden"
      />

      {/* Upload Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Importando Usuarios</h3>
              <button
                type="button"
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700"
                disabled={isUploading && !uploadError}
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {uploadError ? (
              <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                  <p className="text-sm text-red-700">{uploadError}</p>
                </div>
              </div>
            ) : (
              <div className="mb-4">
                <div className="h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-blue-600 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  {uploadProgress < 100 ? (
                    "Procesando archivo..."
                  ) : (
                    <span className="flex items-center text-green-600">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Importación completada
                    </span>
                  )}
                </p>
              </div>
            )}

            <div className="flex justify-end">
              <Button
                variant={uploadError ? "outline" : "default"}
                onClick={closeModal}
                disabled={isUploading && !uploadError}
              >
                {uploadError
                  ? "Cerrar"
                  : uploadProgress === 100
                    ? "Aceptar"
                    : "Cancelar"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
