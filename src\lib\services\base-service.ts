/**
 * @fileoverview Base Service Class - Foundation for all business logic services
 *
 * This file provides the base service implementation that all domain services extend.
 * It implements common patterns for business logic operations, error handling, validation,
 * and response formatting to ensure consistency across all services.
 *
 * Services contain business logic and orchestrate operations across multiple repositories.
 * They provide a clean API for the presentation layer while keeping business rules centralized.
 *
 * @example Basic Service Implementation
 * ```typescript
 * import { BaseService, ServiceResponse } from './base-service';
 * import { RepositoryFactory } from '../repositories';
 *
 * export class MyService extends BaseService {
 *   constructor(repositories: RepositoryFactory) {
 *     super(repositories);
 *   }
 *
 *   async createItem(data: any): Promise<ServiceResponse<any>> {
 *     // Validation
 *     const validationError = this.validateRequired(data.name, 'name');
 *     if (validationError) return validationError;
 *
 *     // Business logic
 *     return this.handleRepositoryOperation(
 *       async () => await this.repositories.items.create(data),
 *       'Failed to create item'
 *     );
 *   }
 * }
 * ```
 *
 * <AUTHOR> CURSE Development Team
 * @version 1.0.0
 * @since 2025-01-12
 */

import type { RepositoryFactory } from "../repositories";

// ============================================================================
// Service Response Types
// ============================================================================

/**
 * Standard service response wrapper for all service operations
 *
 * @template T The type of data returned on success
 *
 * @example Success Response
 * ```typescript
 * const response: ServiceResponse<User> = {
 *   success: true,
 *   data: { id: '123', name: 'John Doe' },
 *   message: 'User created successfully'
 * };
 * ```
 *
 * @example Error Response
 * ```typescript
 * const response: ServiceResponse = {
 *   success: false,
 *   error: {
 *     code: 'VALIDATION_ERROR',
 *     message: 'Email is required',
 *     field: 'email'
 *   }
 * };
 * ```
 */
export interface ServiceResponse<T = unknown> {
  /** Indicates if the operation was successful */
  success: boolean;
  /** The data returned on successful operations */
  data?: T;
  /** Error information when operation fails */
  error?: ServiceError;
  /** Optional human-readable message */
  message?: string;
}

/**
 * Standardized error structure for service operations
 *
 * @example Validation Error
 * ```typescript
 * const error: ServiceError = {
 *   code: 'VALIDATION_ERROR',
 *   message: 'Email format is invalid',
 *   field: 'email',
 *   details: { providedValue: 'invalid-email' }
 * };
 * ```
 */
export interface ServiceError {
  /** Unique error code for programmatic handling */
  code: string;
  /** Human-readable error message */
  message: string;
  /** Additional error context or debugging information */
  details?: unknown;
  /** Specific field name for validation errors */
  field?: string;
}

/**
 * Options for paginated queries
 *
 * @example Basic Pagination
 * ```typescript
 * const options: PaginationOptions = {
 *   page: 1,
 *   limit: 10,
 *   orderBy: 'created_at',
 *   orderDirection: 'desc'
 * };
 * ```
 */
export interface PaginationOptions {
  /** Page number (1-based) */
  page?: number;
  /** Number of items per page (max 100) */
  limit?: number;
  /** Field to order by */
  orderBy?: string;
  /** Sort direction */
  orderDirection?: "asc" | "desc";
}

/**
 * Paginated response wrapper with metadata
 *
 * @template T The type of items in the data array
 *
 * @example Paginated Users Response
 * ```typescript
 * const response: PaginatedResponse<User> = {
 *   data: [{ id: '1', name: 'John' }, { id: '2', name: 'Jane' }],
 *   pagination: {
 *     page: 1,
 *     limit: 10,
 *     total: 25,
 *     totalPages: 3,
 *     hasNext: true,
 *     hasPrev: false
 *   }
 * };
 * ```
 */
export interface PaginatedResponse<T> {
  /** Array of items for the current page */
  data: T[];
  /** Pagination metadata */
  pagination: {
    /** Current page number */
    page: number;
    /** Items per page */
    limit: number;
    /** Total number of items */
    total: number;
    /** Total number of pages */
    totalPages: number;
    /** Whether there are more pages after current */
    hasNext: boolean;
    /** Whether there are pages before current */
    hasPrev: boolean;
  };
}

// ============================================================================
// Base Service Implementation
// ============================================================================

/**
 * Abstract base class for all business logic services
 *
 * Provides common functionality for error handling, validation, pagination,
 * and response formatting. All domain services should extend this class.
 *
 * @abstract
 * @class BaseService
 *
 * @example Extending BaseService
 * ```typescript
 * export class UserService extends BaseService {
 *   constructor(repositories: RepositoryFactory) {
 *     super(repositories);
 *   }
 *
 *   async createUser(data: CreateUserRequest): Promise<ServiceResponse<User>> {
 *     const validationError = this.validateCreateUserRequest(data);
 *     if (validationError) return validationError;
 *
 *     return this.handleRepositoryOperation(
 *       async () => await this.repositories.users.create(data),
 *       'Failed to create user'
 *     );
 *   }
 * }
 * ```
 */
export abstract class BaseService {
  /** Repository factory for accessing data layer */
  protected repositories: RepositoryFactory;

  /**
   * Creates a new service instance
   *
   * @param repositories - Factory providing access to all repositories
   */
  constructor(repositories: RepositoryFactory) {
    this.repositories = repositories;
  }

  // ============================================================================
  // Response Helpers
  // ============================================================================

  /**
   * Creates a successful service response
   *
   * @template T The type of data being returned
   * @param data - The data to return
   * @param message - Optional success message
   * @returns Formatted success response
   *
   * @example
   * ```typescript
   * return this.success(user, 'User created successfully');
   * ```
   */
  protected success<T>(data: T, message?: string): ServiceResponse<T> {
    return {
      success: true,
      data,
      message,
    };
  }

  /**
   * Creates an error service response
   *
   * @param code - Unique error code for programmatic handling
   * @param message - Human-readable error message
   * @param details - Additional error context
   * @param field - Specific field name for validation errors
   * @returns Formatted error response
   *
   * @example
   * ```typescript
   * return this.error('VALIDATION_ERROR', 'Email is required', null, 'email');
   * ```
   */
  protected error(
    code: string,
    message: string,
    details?: unknown,
    field?: string,
  ): ServiceResponse {
    return {
      success: false,
      error: {
        code,
        message,
        details,
        field,
      },
    };
  }

  protected validationError(field: string, message: string): ServiceResponse {
    return this.error("VALIDATION_ERROR", message, null, field);
  }

  protected notFoundError(resource: string, id?: string): ServiceResponse {
    const message = id
      ? `${resource} with id ${id} not found`
      : `${resource} not found`;

    return this.error("NOT_FOUND", message);
  }

  protected unauthorizedError(
    message: string = "Unauthorized access",
  ): ServiceResponse {
    return this.error("UNAUTHORIZED", message);
  }

  protected forbiddenError(
    message: string = "Access forbidden",
  ): ServiceResponse {
    return this.error("FORBIDDEN", message);
  }

  protected conflictError(message: string): ServiceResponse {
    return this.error("CONFLICT", message);
  }

  protected internalError(message: string, details?: unknown): ServiceResponse {
    return this.error("INTERNAL_ERROR", message, details);
  }

  // ============================================================================
  // Pagination Helpers
  // ============================================================================

  protected calculatePagination(
    total: number,
    page: number = 1,
    limit: number = 10,
  ): PaginatedResponse<unknown>["pagination"] {
    const totalPages = Math.ceil(total / limit);

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  protected validatePaginationOptions(options: PaginationOptions): {
    page: number;
    limit: number;
    offset: number;
  } {
    const page = Math.max(1, options.page || 1);
    const limit = Math.min(100, Math.max(1, options.limit || 10)); // Max 100 items per page
    const offset = (page - 1) * limit;

    return { page, limit, offset };
  }

  // ============================================================================
  // Validation Helpers
  // ============================================================================

  protected validateRequired(
    value: unknown,
    fieldName: string,
  ): ServiceResponse | null {
    if (value === null || value === undefined || value === "") {
      return this.validationError(fieldName, `${fieldName} is required`);
    }
    return null;
  }

  protected validateEmail(email: string): ServiceResponse | null {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return this.validationError("email", "Invalid email format");
    }
    return null;
  }

  protected validateLength(
    value: string,
    fieldName: string,
    min?: number,
    max?: number,
  ): ServiceResponse | null {
    if (min && value.length < min) {
      return this.validationError(
        fieldName,
        `${fieldName} must be at least ${min} characters long`,
      );
    }
    if (max && value.length > max) {
      return this.validationError(
        fieldName,
        `${fieldName} must be no more than ${max} characters long`,
      );
    }
    return null;
  }

  protected validateUUID(
    value: string,
    fieldName: string,
  ): ServiceResponse | null {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(value)) {
      return this.validationError(
        fieldName,
        `${fieldName} must be a valid UUID`,
      );
    }
    return null;
  }

  // ============================================================================
  // Error Handling Helpers
  // ============================================================================

  protected async handleRepositoryOperation<T>(
    operation: () => Promise<T>,
    errorMessage: string = "Operation failed",
  ): Promise<ServiceResponse<T>> {
    try {
      const result = await operation();
      return this.success(result);
    } catch (error: unknown) {
      console.error(`Repository operation failed: ${errorMessage}`, error);

      // Map common database errors to service errors
      if (error.message?.includes("not found")) {
        return this.notFoundError("Resource");
      }

      if (
        error.message?.includes("unique constraint") ||
        error.message?.includes("duplicate key")
      ) {
        return this.conflictError("Resource already exists");
      }

      if (error.message?.includes("foreign key constraint")) {
        return this.validationError(
          "reference",
          "Referenced resource does not exist",
        );
      }

      return this.internalError(errorMessage, error.message);
    }
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  protected generateId(): string {
    // Simple UUID v4 generator (in production, use a proper UUID library)
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  protected sanitizeString(value: string): string {
    return value.trim().replace(/\s+/g, " ");
  }

  protected formatDate(date: Date | string): string {
    const d = typeof date === "string" ? new Date(date) : date;
    return d.toISOString();
  }

  protected isValidDate(date: string): boolean {
    const d = new Date(date);
    return d instanceof Date && !Number.isNaN(d.getTime());
  }

  // ============================================================================
  // Logging Helpers
  // ============================================================================

  protected log(
    level: "info" | "warn" | "error",
    message: string,
    data?: unknown,
  ): void {
    const timestamp = new Date().toISOString();
    const serviceName = this.constructor.name;

    const logMessage = `[${timestamp}] [${level.toUpperCase()}] [${serviceName}] ${message}`;

    switch (level) {
      case "info":
        console.log(logMessage, data || "");
        break;
      case "warn":
        console.warn(logMessage, data || "");
        break;
      case "error":
        console.error(logMessage, data || "");
        break;
    }
  }

  protected logInfo(message: string, data?: unknown): void {
    this.log("info", message, data);
  }

  protected logWarn(message: string, data?: unknown): void {
    this.log("warn", message, data);
  }

  protected logError(message: string, data?: unknown): void {
    this.log("error", message, data);
  }
}
