"use client";

import DOMPurify from "dompurify";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";

interface CertificateTemplate {
  id: string;
  name: string;
  html_content: string;
  css_styles: string;
  variables: Record<string, unknown>;
}

export default function CertificadoPreviewPage() {
  const params = useParams();
  const templateId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [template, setTemplate] = useState<CertificateTemplate | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTemplate() {
      try {
        setLoading(true);
        setError(null);

        // Intentar obtener la plantilla específica
        const { data, error } = await supabase
          .from("certificate_templates")
          .select("*")
          .eq("id", templateId)
          .single();

        if (!error && data) {
          setTemplate(data);
        } else {
          // Si hay un error, intentar obtener la plantilla predeterminada
          console.warn(
            "Error al obtener plantilla específica, intentando con la predeterminada:",
            error,
          );

          const { data: defaultTemplate, error: defaultError } = await supabase
            .from("certificate_templates")
            .select("*")
            .eq("is_default", true)
            .single();

          if (!defaultError && defaultTemplate) {
            setTemplate(defaultTemplate);
          } else {
            // Si no hay plantilla predeterminada, intentar obtener cualquier plantilla
            console.warn(
              "Error al obtener plantilla predeterminada, intentando con cualquier plantilla:",
              defaultError,
            );

            const { data: anyTemplate, error: anyError } = await supabase
              .from("certificate_templates")
              .select("*")
              .limit(1)
              .single();

            if (!anyError && anyTemplate) {
              setTemplate(anyTemplate);
            } else {
              // Si no se encuentra ninguna plantilla, mostrar error
              throw new Error(
                "No se pudo encontrar ninguna plantilla de certificado",
              );
            }
          }
        }
      } catch (error: unknown) {
        console.error("Error fetching template:", error);
        setError(error instanceof Error ? error.message : "Error desconocido");
      } finally {
        setLoading(false);
      }
    }

    if (templateId) {
      fetchTemplate();
    }
  }, [templateId]);

  // Replace template variables with sample data
  const processTemplate = (html: string) => {
    try {
      if (!html) return "<div>No hay contenido HTML en esta plantilla</div>";

      // Define sample data for template variables
      const sampleData = {
        participantName: "Juan Pérez González",
        courseTitle: "Manejo de Extintores y Combate de Incendios",
        certificateNumber: "CERT-2023-001",
        completionDate: "15 de junio de 2023",
        duration: "8 Horas",
        instructorName: "Carlos Rodríguez",
        issuingAuthority: "QR CURSE",
        logo: '<img src="/logo.svg" alt="DOMUS Logo" style="width: 100%; height: 100%; object-fit: contain;" />',
        qrCode: `<img src="/sample-qr.png" alt="Código QR de verificación" style="max-width: 100%; max-height: 100%; object-fit: contain; display: block;" />`,
        instructorSignature: `<img src="/sample-signature.png" alt="Firma del instructor" style="max-width: 150px; max-height: 60px; object-fit: contain; display: block; margin: 0 auto;" />`,
      };

      // Replace all variables in the template
      let processedHtml = html;
      Object.entries(sampleData).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, "g");
        processedHtml = processedHtml.replace(regex, value);
      });

      return processedHtml;
    } catch (error: unknown) {
      console.error("Error processing template:", error);
      return `<div class="error">Error al procesar la plantilla: ${error instanceof Error ? error.message : "Error desconocido"}</div>`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Cargando vista previa...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 max-w-md">
          <h3 className="text-lg font-medium">Error al cargar la plantilla</h3>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>No se encontró la plantilla</p>
      </div>
    );
  }

  const processedHtml = processTemplate(template.html_content);

  // Agregar estilos adicionales para asegurar que el certificado se ajuste al contenedor
  const additionalStyles = `
    .certificate-container {
      width: 100%;
      max-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow-x: hidden;
      padding: 0;
      margin: 0;
    }
    .certificate-content {
      width: 100%;
      max-width: 100%;
      height: auto;
      box-sizing: border-box;
    }
    /* Estilos para el certificado */
    .certificate {
      width: 100% !important;
      max-width: 100% !important;
      height: auto !important;
      margin: 0 auto !important;
      box-sizing: border-box !important;
    }
    /* Asegurar que las imágenes se ajusten correctamente */
    img {
      max-width: 100%;
      height: auto;
    }
    /* Asegurar que las tablas se ajusten correctamente */
    table {
      width: 100%;
      max-width: 100%;
    }
    /* Ajustar el tamaño del título para que sea proporcional al logo */
    .title {
      font-size: 3.5em !important;
      margin-top: 150px !important;
      margin-bottom: 20px !important;
      letter-spacing: 1px !important;
    }
    /* Aumentar el tamaño del logo */
    .logo {
      width: 180px !important;
      height: 100px !important;
    }
    /* Asegurar que el QR se muestre correctamente */
    .qr-code {
      overflow: hidden !important;
      box-sizing: border-box !important;
    }
    .qr-code > div.qr-image {
      width: 120px !important;
      height: 120px !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      overflow: hidden !important;
      margin-bottom: 5px !important;
    }
    .qr-code img {
      max-width: 100% !important;
      max-height: 100% !important;
      object-fit: contain !important;
      display: block !important;
    }
    .qr-text {
      font-size: 11px !important;
      text-align: center !important;
      font-weight: bold !important;
      color: #219bf9 !important;
      background-color: white !important;
      padding: 2px 5px !important;
      border-radius: 3px !important;
      width: 100% !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
    /* Mejorar el estilo del nombre del estudiante */
    .participant-name {
      font-size: 2.5em !important;
      color: #007bff !important;
      font-weight: bold !important;
      margin: 15px 0 !important;
      text-transform: uppercase !important;
      letter-spacing: 1px !important;
    }
    /* Mejorar el estilo del documento de identidad */
    .document-number {
      font-size: 1.4em !important;
      color: #444 !important;
      font-weight: bold !important;
      border: 1px solid #ddd !important;
      display: inline-block !important;
      padding: 5px 15px !important;
      border-radius: 5px !important;
      background-color: rgba(255, 255, 255, 0.7) !important;
      margin-bottom: 25px !important;
    }
    /* Mejorar el estilo del título del curso */
    .course-title {
      font-size: 1.8em !important;
      color: #333 !important;
      text-align: center !important;
      margin-bottom: 30px !important;
      font-weight: bold !important;
      padding: 0 20px !important;
    }
  `;

  // Sanitize HTML content for security
  // Justificación: El uso de dangerouslySetInnerHTML es necesario para renderizar plantillas de certificados personalizables.
  // Se utiliza DOMPurify para sanitizar el HTML y minimizar riesgos de XSS. Si se requiere mayor seguridad, revisar los tags y atributos permitidos.
  const sanitizedHtml = DOMPurify.sanitize(processedHtml, {
    ALLOWED_TAGS: [
      "div",
      "span",
      "p",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "img",
      "table",
      "tr",
      "td",
      "th",
      "tbody",
      "thead",
      "br",
      "strong",
      "em",
      "b",
      "i",
      "u",
    ],
    ALLOWED_ATTR: ["class", "style", "src", "alt", "width", "height", "id"],
    ALLOW_DATA_ATTR: false,
    // Reforzar: No permitir eventos ni javascript: en atributos
    FORBID_ATTR: [
      "onerror",
      "onclick",
      "onload",
      "onmouseover",
      "onfocus",
      "onblur",
    ],
    FORBID_TAGS: [
      "script",
      "iframe",
      "object",
      "embed",
      "form",
      "input",
      "button",
      "link",
      "meta",
    ],
  });

  // Sanitize CSS styles
  // Justificación: El uso de dangerouslySetInnerHTML para estilos es necesario para plantillas personalizadas.
  // Se sanitiza para evitar inyección de CSS malicioso.
  const sanitizedStyles = DOMPurify.sanitize(
    template.css_styles + additionalStyles,
    {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: [],
      FORBID_TAGS: [
        "script",
        "iframe",
        "object",
        "embed",
        "form",
        "input",
        "button",
        "link",
        "meta",
      ],
    },
  );

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div
          className="bg-white certificate-container"
          style={{ minHeight: "auto" }}
        >
          <style
            // biome-ignore lint/security/noDangerouslySetInnerHtml: Necesario para estilos de plantillas, sanitizado con DOMPurify
            dangerouslySetInnerHTML={{
              __html: sanitizedStyles,
            }}
          />
          <div
            className="certificate-content"
            // biome-ignore lint/security/noDangerouslySetInnerHtml: Necesario para renderizar plantillas de certificados, sanitizado con DOMPurify
            dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
          />
        </div>
      </div>
    </div>
  );
}
