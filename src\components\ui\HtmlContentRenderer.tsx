import DOMPurify from "dompurify";
import React, { useEffect, useRef } from "react";

interface HtmlContentRendererProps {
  htmlContent: string;
  cssStyles?: string;
}

const HtmlContentRenderer: React.FC<HtmlContentRendererProps> = ({
  htmlContent,
  cssStyles,
}) => {
  const sanitizedHtml = DOMPurify.sanitize(htmlContent);
  const sanitizedCss = cssStyles ? DOMPurify.sanitize(cssStyles) : undefined;

  const styleRef = useRef<HTMLStyleElement>(null);
  const divRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (styleRef.current && sanitizedCss) {
      styleRef.current.innerHTML = sanitizedCss;
    }
    if (divRef.current) {
      divRef.current.innerHTML = sanitizedHtml;
    }
  }, [sanitizedHtml, sanitizedCss]);

  return (
    <>
      {sanitizedCss && <style ref={styleRef} />}
      <div ref={divRef} />
    </>
  );
};

export default HtmlContentRenderer;
