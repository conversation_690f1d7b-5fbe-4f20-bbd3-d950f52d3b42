"use client";

import { Check, ChevronDown, ChevronUp, Search, User, X } from "lucide-react";
import * as React from "react";
import { cn } from "@/lib/utils";

export interface UserOption {
  value: string;
  label: string;
  email?: string;
  identity_document?: string;
}

interface SimpleUserSelectProps {
  options: UserOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  emptyMessage?: string;
  className?: string;
  disabled?: boolean;
}

export function SimpleUserSelect({
  options,
  value,
  onChange,
  placeholder = "Seleccionar usuario...",
  emptyMessage = "No se encontraron usuarios.",
  className,
  disabled = false,
}: SimpleUserSelectProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Ensure we have valid options
  const validOptions = React.useMemo(() => {
    return Array.isArray(options) ? options : [];
  }, [options]);

  // Filter options based on search query
  const filteredOptions = React.useMemo(() => {
    if (!searchQuery) return validOptions;

    const lowerQuery = searchQuery.toLowerCase();
    return validOptions.filter(
      (option) =>
        option.label.toLowerCase().includes(lowerQuery) ||
        option.email?.toLowerCase().includes(lowerQuery) ||
        option.identity_document?.toLowerCase().includes(lowerQuery),
    );
  }, [validOptions, searchQuery]);

  // Handle selection
  const handleSelect = (option: UserOption) => {
    console.log("SimpleUserSelect selecting option:", option);
    // Force a small delay to ensure the UI updates correctly
    setTimeout(() => {
      onChange(option.value);
      setIsOpen(false);
      setSearchQuery("");
    }, 10);
  };

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className={cn("relative w-full", className)} ref={containerRef}>
      {/* Trigger button */}
      <button
        type="button"
        className={cn(
          "flex w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
          "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          disabled && "opacity-50 cursor-not-allowed",
          className,
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
      >
        <div className="flex-1 text-left overflow-hidden text-ellipsis whitespace-nowrap">
          {value ? (
            validOptions.find((option) => option.value === value)?.label ||
            placeholder
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
        </div>
        <div className="flex items-center">
          {value && (
            <X
              className="mr-1 h-4 w-4 shrink-0 opacity-70 hover:opacity-100 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onChange("");
              }}
            />
          )}
          {isOpen ? (
            <ChevronUp className="h-4 w-4 shrink-0 opacity-50" />
          ) : (
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          )}
        </div>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 rounded-md border border-input bg-background shadow-md">
          {/* Search input */}
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              type="text"
              className="flex h-10 w-full bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground"
              placeholder="Buscar por nombre, email o RUT..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Options list */}
          <div className="max-h-[300px] overflow-y-auto p-1">
            {filteredOptions.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                {emptyMessage}
              </div>
            ) : (
              filteredOptions.map((option) => (
                <button
                  type="button"
                  key={option.value}
                  className={cn(
                    "flex w-full items-center px-2 py-1.5 text-sm rounded-sm",
                    "hover:bg-accent hover:text-accent-foreground",
                    value === option.value &&
                      "bg-accent text-accent-foreground",
                  )}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log("Option clicked:", option);
                    handleSelect(option);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      handleSelect(option);
                    }
                  }}
                >
                  <User className="mr-2 h-4 w-4 shrink-0" />
                  <div className="flex flex-col flex-1 min-w-0">
                    <span className="font-medium truncate">{option.label}</span>
                    <div className="flex flex-col text-xs text-muted-foreground">
                      {option.email && (
                        <span className="truncate">{option.email}</span>
                      )}
                      {option.identity_document && (
                        <span className="truncate">
                          RUT: {option.identity_document}
                        </span>
                      )}
                    </div>
                  </div>
                  {value === option.value && (
                    <Check className="ml-auto h-4 w-4" />
                  )}
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
