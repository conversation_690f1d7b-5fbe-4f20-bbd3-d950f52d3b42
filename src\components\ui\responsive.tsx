/**
 * @fileoverview Responsive Components and Utilities
 *
 * Componentes y utilidades para diseño responsive:
 * - Breakpoint hooks
 * - Container responsive
 * - Grid adaptativo
 * - Componentes que se adaptan al viewport
 */

"use client";

import Image from "next/image";
import * as React from "react";
import { cn } from "@/lib/utils";

// ============================================================================
// Responsive Hooks
// ============================================================================

/**
 * Hook para detectar breakpoints
 */
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = React.useState<string>("sm");

  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width >= 1536) setBreakpoint("2xl");
      else if (width >= 1280) setBreakpoint("xl");
      else if (width >= 1024) setBreakpoint("lg");
      else if (width >= 768) setBreakpoint("md");
      else if (width >= 640) setBreakpoint("sm");
      else setBreakpoint("xs");
    };

    updateBreakpoint();
    window.addEventListener("resize", updateBreakpoint);
    return () => window.removeEventListener("resize", updateBreakpoint);
  }, []);

  const isXs = breakpoint === "xs";
  const isSm = breakpoint === "sm";
  const isMd = breakpoint === "md";
  const isLg = breakpoint === "lg";
  const isXl = breakpoint === "xl";
  const is2Xl = breakpoint === "2xl";

  const isMobile = isXs || isSm;
  const isTablet = isMd;
  const isDesktop = isLg || isXl || is2Xl;

  return {
    breakpoint,
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    is2Xl,
    isMobile,
    isTablet,
    isDesktop,
  };
}

/**
 * Hook para detectar orientación del dispositivo
 */
export function useOrientation() {
  const [orientation, setOrientation] = React.useState<
    "portrait" | "landscape"
  >("portrait");

  React.useEffect(() => {
    const updateOrientation = () => {
      setOrientation(
        window.innerHeight > window.innerWidth ? "portrait" : "landscape",
      );
    };

    updateOrientation();
    window.addEventListener("resize", updateOrientation);
    return () => window.removeEventListener("resize", updateOrientation);
  }, []);

  return orientation;
}

/**
 * Hook para detectar viewport dimensions
 */
export function useViewport() {
  const [viewport, setViewport] = React.useState({
    width: 0,
    height: 0,
  });

  React.useEffect(() => {
    const updateViewport = () => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateViewport();
    window.addEventListener("resize", updateViewport);
    return () => window.removeEventListener("resize", updateViewport);
  }, []);

  return viewport;
}

// ============================================================================
// Responsive Components
// ============================================================================

/**
 * Container responsive con máximos anchos adaptativos
 */
interface ResponsiveContainerProps
  extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "full";
  padding?: "none" | "sm" | "md" | "lg";
  children: React.ReactNode;
}

const containerSizes = {
  sm: "max-w-screen-sm",
  md: "max-w-screen-md",
  lg: "max-w-screen-lg",
  xl: "max-w-screen-xl",
  "2xl": "max-w-screen-2xl",
  full: "max-w-full",
};

const containerPadding = {
  none: "",
  sm: "px-4",
  md: "px-6",
  lg: "px-8",
};

export function ResponsiveContainer({
  size = "xl",
  padding = "md",
  className,
  children,
  ...props
}: ResponsiveContainerProps) {
  return (
    <div
      className={cn(
        "mx-auto w-full",
        containerSizes[size],
        containerPadding[padding],
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
}

/**
 * Grid responsive adaptativo
 */
interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    "2xl"?: number;
  };
  gap?: "sm" | "md" | "lg" | "xl";
  children: React.ReactNode;
}

const gridGaps = {
  sm: "gap-2",
  md: "gap-4",
  lg: "gap-6",
  xl: "gap-8",
};

export function ResponsiveGrid({
  columns = { xs: 1, sm: 2, md: 3, lg: 4 },
  gap = "md",
  className,
  children,
  ...props
}: ResponsiveGridProps) {
  const gridClasses = React.useMemo(() => {
    const classes = ["grid"];

    if (columns.xs) classes.push(`grid-cols-${columns.xs}`);
    if (columns.sm) classes.push(`sm:grid-cols-${columns.sm}`);
    if (columns.md) classes.push(`md:grid-cols-${columns.md}`);
    if (columns.lg) classes.push(`lg:grid-cols-${columns.lg}`);
    if (columns.xl) classes.push(`xl:grid-cols-${columns.xl}`);
    if (columns["2xl"]) classes.push(`2xl:grid-cols-${columns["2xl"]}`);

    return classes.join(" ");
  }, [columns]);

  return (
    <div className={cn(gridClasses, gridGaps[gap], className)} {...props}>
      {children}
    </div>
  );
}

/**
 * Componente que se muestra/oculta según breakpoint
 */
interface ShowAtProps {
  breakpoint: "xs" | "sm" | "md" | "lg" | "xl" | "2xl";
  children: React.ReactNode;
  className?: string;
}

const showAtClasses = {
  xs: "block sm:hidden",
  sm: "hidden sm:block md:hidden",
  md: "hidden md:block lg:hidden",
  lg: "hidden lg:block xl:hidden",
  xl: "hidden xl:block 2xl:hidden",
  "2xl": "hidden 2xl:block",
};

export function ShowAt({ breakpoint, children, className }: ShowAtProps) {
  return (
    <div className={cn(showAtClasses[breakpoint], className)}>{children}</div>
  );
}

/**
 * Componente que se oculta a partir de un breakpoint
 */
interface HideAtProps {
  breakpoint: "xs" | "sm" | "md" | "lg" | "xl" | "2xl";
  children: React.ReactNode;
  className?: string;
}

const hideAtClasses = {
  xs: "hidden",
  sm: "sm:hidden",
  md: "md:hidden",
  lg: "lg:hidden",
  xl: "xl:hidden",
  "2xl": "2xl:hidden",
};

export function HideAt({ breakpoint, children, className }: HideAtProps) {
  return (
    <div className={cn(hideAtClasses[breakpoint], className)}>{children}</div>
  );
}

/**
 * Stack responsive (cambia de horizontal a vertical)
 */
interface ResponsiveStackProps extends React.HTMLAttributes<HTMLDivElement> {
  direction?: {
    xs?: "row" | "col";
    sm?: "row" | "col";
    md?: "row" | "col";
    lg?: "row" | "col";
  };
  gap?: "sm" | "md" | "lg" | "xl";
  align?: "start" | "center" | "end" | "stretch";
  justify?: "start" | "center" | "end" | "between" | "around" | "evenly";
  children: React.ReactNode;
}

export function ResponsiveStack({
  direction = { xs: "col", md: "row" },
  gap = "md",
  align = "start",
  justify = "start",
  className,
  children,
  ...props
}: ResponsiveStackProps) {
  const stackClasses = React.useMemo(() => {
    const classes = ["flex"];

    // Direction
    if (direction.xs)
      classes.push(direction.xs === "row" ? "flex-row" : "flex-col");
    if (direction.sm)
      classes.push(direction.sm === "row" ? "sm:flex-row" : "sm:flex-col");
    if (direction.md)
      classes.push(direction.md === "row" ? "md:flex-row" : "md:flex-col");
    if (direction.lg)
      classes.push(direction.lg === "row" ? "lg:flex-row" : "lg:flex-col");

    // Gap
    const gapMap = { sm: "2", md: "4", lg: "6", xl: "8" };
    classes.push(`gap-${gapMap[gap]}`);

    // Align
    const alignMap = {
      start: "items-start",
      center: "items-center",
      end: "items-end",
      stretch: "items-stretch",
    };
    classes.push(alignMap[align]);

    // Justify
    const justifyMap = {
      start: "justify-start",
      center: "justify-center",
      end: "justify-end",
      between: "justify-between",
      around: "justify-around",
      evenly: "justify-evenly",
    };
    classes.push(justifyMap[justify]);

    return classes.join(" ");
  }, [direction, gap, align, justify]);

  return (
    <div className={cn(stackClasses, className)} {...props}>
      {children}
    </div>
  );
}

/**
 * Texto responsive con tamaños adaptativos
 */
interface ResponsiveTextProps extends React.HTMLAttributes<HTMLElement> {
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p" | "span";
  size?: {
    xs?: "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "4xl";
    sm?: "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "4xl";
    md?: "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "4xl";
    lg?: "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "4xl";
  };
  weight?: "normal" | "medium" | "semibold" | "bold";
  children: React.ReactNode;
}

export function ResponsiveText({
  as: Component = "p",
  size = { xs: "base", md: "lg" },
  weight = "normal",
  className,
  children,
  ...props
}: ResponsiveTextProps) {
  const textClasses = React.useMemo(() => {
    const classes = [];

    // Sizes
    if (size.xs) classes.push(`text-${size.xs}`);
    if (size.sm) classes.push(`sm:text-${size.sm}`);
    if (size.md) classes.push(`md:text-${size.md}`);
    if (size.lg) classes.push(`lg:text-${size.lg}`);

    // Weight
    const weightMap = {
      normal: "font-normal",
      medium: "font-medium",
      semibold: "font-semibold",
      bold: "font-bold",
    };
    classes.push(weightMap[weight]);

    return classes.join(" ");
  }, [size, weight]);

  return (
    <Component className={cn(textClasses, className)} {...props}>
      {children}
    </Component>
  );
}

/**
 * Imagen responsive con lazy loading
 */
interface ResponsiveImageProps
  extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  aspectRatio?: "square" | "video" | "portrait" | "landscape";
  objectFit?: "cover" | "contain" | "fill" | "none";
  lazy?: boolean;
}

const aspectRatios = {
  square: "aspect-square",
  video: "aspect-video",
  portrait: "aspect-[3/4]",
  landscape: "aspect-[4/3]",
};

export function ResponsiveImage({
  src,
  alt,
  aspectRatio,
  objectFit = "cover",
  lazy = true,
  className,
  ...props
}: ResponsiveImageProps) {
  return (
    <div
      className={cn(
        "relative overflow-hidden",
        aspectRatio && aspectRatios[aspectRatio],
        className,
      )}
    >
      <Image
        src={src}
        alt={alt}
        fill
        loading={lazy ? "lazy" : "eager"}
        className={cn(
          "w-full h-full",
          objectFit === "cover" && "object-cover",
          objectFit === "contain" && "object-contain",
          objectFit === "fill" && "object-fill",
          objectFit === "none" && "object-none",
        )}
        {...props}
      />
    </div>
  );
}
