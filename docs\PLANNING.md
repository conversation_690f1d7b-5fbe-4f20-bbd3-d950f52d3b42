# 📝 PLANNING.md — DOMUS OTEC Project Constitution

_Last Updated: 2025-01-13_

## 🚀 Project Status Overview

**Current Implementation: 95% Complete**

### ✅ Major Milestones Achieved (2025-01-13)
- **Panel Unificado Implementado** (100%) - Arquitectura basada en roles completamente funcional
- **Sistema de Autenticación Modernizado** (100%) - Middleware y rutas actualizadas
- **Corrección Masiva de Errores** (100%) - Todos los errores de Biome corregidos
- **Modo Oscuro Funcional** (100%) - Sistema de temas completamente operativo
- **Accesibilidad WCAG 2.1 AA** (80%) - Cumplimiento parcial implementado
- **Tipos TypeScript Seguros** (90%) - Eliminación de tipos `any` y non-null assertions
- **Next.js 15.3.5 Actualizado** (100%) - Última versión con Turbopack estable
- **Dependencias Modernizadas** (100%) - Todas las dependencias actualizadas a versiones estables
- **Configuración Optimizada** (100%) - Next.js config con características modernas

### 🔄 Completado Recientemente (2025-01-13)
- **Corrección de Non-null Assertions** (100%) - Validaciones seguras implementadas
- **Organización de Imports** (100%) - Reorganización según reglas de Biome completada
- **Modernización de Dependencias** (100%) - Actualización sistemática completada

## Table of Contents

1. [Project Vision & Purpose](#1-project-vision--purpose)
2. [Tech Stack & Architecture](#2-tech-stack--architecture)
3. [Key Principles & Constraints](#3-key-principles--constraints)
4. [AI Context & Automation](#4-ai-context--automation)
5. [Feature Epics](#5-feature-epics)
6. [Modular Structure](#6-modular-structure)
7. [How to Extend](#7-how-to-extend)
8. [References](#8-references)

---

## 1. Project Vision & Purpose

**Vision:** A robust, open-source scaffold for launching online course and certification platforms, designed for rapid rebranding, multi-tenancy, and AI coding agent collaboration.

**Purpose:** To provide a production-ready, context-engineered template that enables rapid deployment of course management and certification systems while maintaining high code quality, security, and AI-agent compatibility.

---

## 2. Tech Stack & Architecture

**General Architecture:** Monolithic Next.js application with Supabase backend, designed for easy multi-tenant deployment and AI agent collaboration.

**Tech Stack:**
- **Primary Language:** TypeScript
- **Frontend Framework:** Next.js 14+ (App Router, SSR/SSG, RSC)
- **UI Framework:** React 19, TailwindCSS, Shadcn/ui
- **Backend/Database:** Supabase (PostgreSQL, Auth, Storage, Edge Functions, RLS)
- **Authentication:** Supabase Auth with Row Level Security
- **Deployment:** Vercel (Frontend), Supabase (Backend)
- **Testing:** Playwright (E2E), Vitest (Unit)
- **CI/CD:** GitHub Actions, Biome (lint/format/imports), automated dependency updates
- **AI Tools:** Context Engineering for AI coding assistants

---

## 3. Key Principles & Constraints

**Design Principles:**

- **Context Engineering:** All docs and code are structured for AI agent and human consumption
- **Modularity:** Files < 2000 LoC, comprehensive unit testing, continuous documentation
- **Reusability:** Easy to fork, rebrand, and deploy for any client or vertical
- **Security-First:** RLS, audit logging, and best practices by default

**Constraints:**

- Use environment variables for all secrets and configuration
- Use only well-vetted, popular libraries with active maintenance
- All code must be formatted, linted y ordenado con **Biome** (reemplaza Prettier/ESLint)
- Maintain backward compatibility during refactoring phases
- Document all architectural decisions and business logic
- Eliminar archivos de configuración y scripts de Prettier/ESLint tras la migración a Biome

---

## 4. AI Context & Automation

- All docs and code are structured for both human and AI agent (bot) consumption.
- **Jules**: Automates code changes, refactors, and documentation from issues labeled `jules`. See [AI_CONTEXT.md](./AI_CONTEXT.md).
- **coderabbitai**: Provides code suggestions, refactoring, and documentation in PRs and issues.
- **Renovate/Dependabot**: Keep all dependencies up-to-date via automated PRs, triggering CI/CD and auto-merge if safe.
- **Metadata & Embeddings**: Automated scripts generate project metadata and vector embeddings for LLM/AI agent context, updated on every push to main.
- **Vercel/Google Cloud Build**: Automated deploys for preview and production, with status notifications in PRs.
- **All bots are configured in the bypass list for seamless automation.**
- **All workflows and bot actions are documented in [AI_CONTEXT.md](./AI_CONTEXT.md).**

---

## 5. Feature Epics

- See [TASKS.md](./TASKS.md) for granular tasks and progress.
- See [RULES.md](./RULES.md) for coding rules and workflow.
- See [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md) for best practices.

---

## 6. Panel Unificado Architecture (NEW - 2025-01-13)

### 🏗️ **Arquitectura del Panel Unificado**

El proyecto ha migrado de paneles separados (`panel-admin`, `panel-alumno`) a un **Panel Unificado Inteligente** que se adapta dinámicamente según el rol del usuario.

**Estructura del Panel Unificado:**
```
src/app/panel/
├── layout.tsx              # Layout principal con navegación dinámica
├── page.tsx                # Dashboard adaptativo por rol
├── components/
│   ├── AdminDashboard.tsx  # Dashboard completo para administradores
│   ├── StudentDashboard.tsx # Dashboard personal para estudiantes
│   └── InstructorDashboard.tsx # Dashboard básico para instructores
├── alumnos/                # Gestión de estudiantes (Solo Admin)
├── certificados/           # Gestión de certificados (Admin + Students)
├── cursos/                 # Gestión de cursos (Admin + Instructors)
├── instructores/           # Gestión de instructores (Solo Admin)
├── reportes/               # Analytics y reportes (Solo Admin)
└── mis-certificados/       # Vista personal de certificados (Solo Students)
```

**Beneficios de la Arquitectura Unificada:**
- **Mantenimiento Simplificado**: Una sola implementación vs. múltiples paneles
- **Navegación Inteligente**: Menú que se adapta automáticamente al rol
- **Código Reutilizable**: Componentes compartidos entre roles
- **Escalabilidad**: Fácil agregar nuevos roles y funcionalidades
- **UX Consistente**: Experiencia unificada para todos los usuarios

### 🔐 **Sistema de Roles y Permisos**

| Rol | Dashboard | Alumnos | Certificados | Cursos | Instructores | Reportes | Mis Certificados |
|-----|-----------|---------|--------------|--------|--------------|----------|-------------------|
| **Admin** | ✅ Completo | ✅ Gestión | ✅ Gestión | ✅ Gestión | ✅ Gestión | ✅ Analytics | ❌ N/A |
| **Student** | ✅ Personal | ❌ No | ✅ Solo lectura | ❌ No | ❌ No | ❌ No | ✅ Personal |
| **Instructor** | ✅ Básico | ❌ No | ✅ Emisión | ✅ Asignados | ❌ No | ❌ No | ❌ N/A |

## 7. Modular Structure

- `src/app/panel/` — **Panel Unificado** con navegación dinámica por roles
- `src/app/` — Next.js App Router, páginas públicas, auth
- `src/components/` — UI y componentes de dominio reutilizables
- `src/lib/` — Utilities, Supabase client, helpers
- `public/` — Static assets, logos, images
- `docs/` — Planning, rules, design system, security, backup/migration
- `.github/workflows/` — CI/CD automation

---

## 7. How to Extend

- Add new features as atomic modules/components
- Document all new features in `docs/`
- Update this file with new epics and context as the project evolves
- For rules, see [RULES.md](./RULES.md)
- For design, see [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- For security, see [SECURITY.md](./SECURITY.md)
- For backup/migration, see [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)

---

## 8. References

- [Context Engineering Template](https://github.com/iberi22/context-engineering-template)
- [README.md](../README.md)
- [RULES.md](./RULES.md)
- [TASKS.md](./TASKS.md)
- [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)
- [SECURITY.md](./SECURITY.md)
- [AI_CONTEXT.md](./AI_CONTEXT.md)
- [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)

---

## Modernization Roadmap (Phased Plan)

> See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full modernization plan and phase details.

- **Branding & Documentation:** [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md), [INDEX.md](./INDEX.md)
- **Architecture & Adapters:** [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)
- **CI/CD & Backups:** [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)
- **AI Context & Bots:** [AI_CONTEXT.md](./AI_CONTEXT.md)
- **Onboarding & Sustainability:** [INDEX.md](./INDEX.md)

Each phase is cross-referenced in the relevant manual. All docs follow the [context-engineering-template](https://github.com/iberi22/context-engineering-template) style for modularity and AI-readiness.

---

### Phase 7: Code Quality Automation with Biome

**Objetivo:** Integrar Biome como linter y formateador único en el pipeline de CI/CD para asegurar calidad de código continua y automatizada.

- Biome validará linting y formato en cada PR y push.
- Se automatizarán fixes triviales y se reportarán errores en el pipeline.
- Se documentarán reglas y convenciones para el equipo.
- El objetivo es mantener la calidad y consistencia del código de forma robusta y automatizada.

**Ver detalles y tareas en:** [TASKS.md](./TASKS.md)

---

## Módulo de Reportes - Análisis y Planificación

**Fecha de Inicio:** 2025-01-12
**Estado:** En Desarrollo

### Análisis de Requerimientos

**Tipos de Reportes Identificados:**
1. **Reportes de Certificados**
   - Certificados emitidos por período
   - Certificados por estado (activos, expirados, revocados)
   - Certificados por curso
   - Certificados por instructor

2. **Reportes de Usuarios**
   - Usuarios registrados por período
   - Usuarios por rol (estudiantes, instructores, administradores)
   - Usuarios activos vs inactivos
   - Usuarios por empresa

3. **Reportes de Cursos**
   - Cursos completados por período
   - Cursos por estado (draft, active, completed, cancelled)
   - Rendimiento de cursos (tasa de finalización)
   - Cursos por instructor

4. **Reportes de Rendimiento**
   - Métricas de asistencia
   - Calificaciones promedio
   - Tasa de aprobación por curso
   - Tiempo promedio de finalización

### Funcionalidades Técnicas

**Generación de Reportes:**
- Filtrado por fechas, usuarios, cursos, estados
- Exportación a PDF y Excel
- Visualización de gráficos interactivos
- Programación de reportes automáticos

**Roles de Acceso:**
- **Administradores**: Acceso completo a todos los reportes
- **Instructores**: Reportes de sus cursos asignados
- **Empresas**: Reportes de sus empleados

**Arquitectura Técnica:**
- Componentes React con TypeScript
- Integración con Supabase para datos
- Uso de Chart.js/Recharts para visualizaciones
- Exportación con jsPDF y xlsx
- Sistema de caché para optimización

---

## Dark Mode Implementation (Professional 2025 Standards)

**Status:** ✅ COMPLETE - Professional dark mode implementation with WCAG 2.1 AA compliance

### Theme System Architecture

**Next-Themes Integration:**
- ✅ Properly configured with `attribute="class"`, `defaultTheme="system"`, `enableSystem=true`
- ✅ FOUC prevention with pre-hydration theme detection script
- ✅ Smooth transitions with performance optimization
- ✅ Persistent theme storage with `qr-curse-theme` key

**CSS Variables System:**
- ✅ Comprehensive CSS variables for all UI elements
- ✅ Semantic color naming (primary, secondary, muted, etc.)
- ✅ Layered depth system for cards and surfaces
- ✅ Enhanced shadows and effects for dark mode

**WCAG 2.1 AA Compliance:**
- ✅ Background vs Foreground: 16.2:1 contrast ratio (exceeds 4.5:1 requirement)
- ✅ Primary vs Primary-foreground: 9.1:1 contrast ratio
- ✅ All interactive elements meet accessibility standards
- ✅ Proper focus indicators and keyboard navigation

**Modern UI/UX Features:**
- ✅ Glassmorphism effects with backdrop blur
- ✅ Neumorphism support for elevated elements
- ✅ Micro-animations and smooth transitions
- ✅ Professional dashboard components with theme awareness

**Component Integration:**
- ✅ Navigation with mobile menu dark mode support
- ✅ Dashboard sidebar with collapsible design
- ✅ Modern cards with glass and elevated variants
- ✅ Theme toggle with light/dark/system options
- ✅ All form elements and interactive states

**Performance Optimizations:**
- ✅ FOUC prevention with inline script
- ✅ Transition disabling during theme changes
- ✅ Optimized CSS variable inheritance
- ✅ Minimal layout shift during theme switching

### Implementation Files

**Core Theme Files:**
- `src/components/theme/theme-provider.tsx` - Theme context and configuration
- `src/components/theme/theme-toggle.tsx` - Accessible theme toggle component
- `src/styles/themes/enhanced-theme.css` - Complete CSS variables system
- `src/app/layout.tsx` - FOUC prevention and theme initialization

**Configuration:**
- `tailwind.config.js` - Theme-aware color system
- `src/app/globals.css` - Base styles with theme variables

### Best Practices Implemented

1. **System Preference Detection** - Automatically detects user's OS theme preference
2. **Accessibility First** - WCAG 2.1 AA compliant with proper contrast ratios
3. **Performance Optimized** - No flash of unstyled content (FOUC)
4. **Professional Design** - Modern 2025 UI/UX trends with glassmorphism
5. **Developer Experience** - Easy to extend with semantic CSS variables

---

### 🛠️ Ajuste de Distribución de Tarjetas en Dashboard Admin (Feb 2025)

**Problema detectado:**
- Las tarjetas de métricas y Acciones Rápidas se apilaban verticalmente (una debajo de otra) en vez de alinearse horizontalmente en columnas, como en el diseño de referencia.

**Causa:**
- El componente `StaggeredList` envolvía cada tarjeta en un `<div>`, lo que impedía que el grid de Tailwind (`grid-cols-4`) distribuyera correctamente las tarjetas en columnas.

**Solución aplicada:**
- Se eliminaron los wrappers de `StaggeredList` en ambas secciones.
- Ahora las tarjetas de métricas y Acciones Rápidas se renderizan directamente como hijos del grid:
  - Se usa `grid grid-cols-4 gap-4` para la distribución.
  - Esto permite que las tarjetas se alineen horizontalmente, ocupando el ancho disponible y adaptándose al diseño responsivo.

**Resultado:**
- Las tarjetas se muestran una al lado de la otra, alineadas y con el layout esperado según la documentación de Tailwind.
- El dashboard es más claro, profesional y consistente visualmente.

---

### 📱 Investigación y Solución: Grid de Tarjetas 100% Responsive (Feb 2025)

**Objetivo:**
- Garantizar que las tarjetas de métricas y acciones rápidas se vean perfectas en cualquier tamaño de pantalla, desde móviles pequeños hasta monitores grandes, siguiendo las mejores prácticas y tendencias modernas de UI/UX.

**Investigación:**
- Se revisaron patrones en la documentación interna (`dashboard-patterns.md`, `ui-component-guide.md`), componentes utilitarios (`ResponsiveGrid`), y guías de Tailwind CSS.
- Las tendencias actuales recomiendan:
  - **Mobile-first:** Comenzar con una columna (`grid-cols-1`) y aumentar el número de columnas en breakpoints mayores.
  - **Breakpoints progresivos:** Usar `sm:grid-cols-2`, `md:grid-cols-3`, `lg:grid-cols-4` para adaptar el layout de forma fluida.
  - **Consistencia visual:** Mantener el mismo patrón para todas las secciones de tarjetas.
  - **Accesibilidad:** Garantizar suficiente espacio y legibilidad en todos los dispositivos.

**Solución aplicada:**
- Se actualizó el grid de métricas y acciones rápidas a:
  ```jsx
  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    {/* tarjetas */}
  </div>
  ```
- Esto asegura:
  - 1 columna en móviles pequeños
  - 2 columnas en móviles grandes/tablets
  - 3 columnas en tablets grandes/laptops
  - 4 columnas en desktop y monitores grandes
- El layout es fluido, moderno y cumple con los estándares de 2025 para dashboards SaaS.

**Referencias:**
- [Tailwind CSS Responsive Grids](https://tailwindcss.com/docs/grid-template-columns#responsive)
- Documentación interna: `dashboard-patterns.md`, `ui/modern-theme-system.md`
- Tendencias UI/UX: Mobile-first, glassmorphism, micro-interacciones

**Resultado:**
- Las tarjetas se ven perfectamente alineadas y adaptadas en cualquier dispositivo, mejorando la experiencia de usuario y la percepción profesional del producto.

---

### 🌐 Aplicación Global del Patrón Grid Responsive (Feb 2025)

**Áreas impactadas:**
- Panel Admin: métricas, acciones rápidas, actividad reciente, plantillas, cursos, reportes, notas/asistencia.
- Panel Alumno: dashboard, certificados, acciones rápidas, asistencia, notas.
- Listados de cursos, plantillas, certificados, métricas y tablas de reportes.

**Patrón aplicado:**
- Se usó el grid:
  ```jsx
  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    {/* tarjetas */}
  </div>
  ```
- En algunos casos, se adaptó el número de columnas según el contexto (por ejemplo, md:grid-cols-2 para tablas o detalles).

**Beneficios logrados:**
- **UX consistente:** Todas las áreas muestran tarjetas y listas perfectamente alineadas y adaptadas a cualquier dispositivo.
- **Mobile-first:** El contenido es legible y usable en móviles pequeños, tablets y desktop.
- **Escalabilidad visual:** El sistema puede crecer en número de tarjetas sin perder orden ni estética.
- **Mantenimiento sencillo:** Un solo patrón para todo el sistema, fácil de extender y documentar.

**Resultado:**
- El producto cumple con los estándares de UI/UX modernos, es visualmente profesional y robusto para cualquier tamaño de pantalla.

---

> This file is the "constitution" for the project. All other docs are referenced here for modularity and clarity.