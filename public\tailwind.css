@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@700;900&family=Open+Sans:wght@400;600&display=swap");

@theme inline{
  --animation-delay-0: 0s;

  --animation-delay-75: 75ms;

  --animation-delay-100: .1s;

  --animation-delay-150: .15s;

  --animation-delay-200: .2s;

  --animation-delay-300: .3s;

  --animation-delay-500: .5s;

  --animation-delay-700: .7s;

  --animation-delay-1000: 1s;

  --animation-repeat-0: 0;

  --animation-repeat-1: 1;

  --animation-repeat-infinite: infinite;

  --animation-direction-normal: normal;

  --animation-direction-reverse: reverse;

  --animation-direction-alternate: alternate;

  --animation-direction-alternate-reverse: alternate-reverse;

  --animation-fill-mode-none: none;

  --animation-fill-mode-forwards: forwards;

  --animation-fill-mode-backwards: backwards;

  --animation-fill-mode-both: both;

  --percentage-0: 0;

  --percentage-5: .05;

  --percentage-10: .1;

  --percentage-15: .15;

  --percentage-20: .2;

  --percentage-25: .25;

  --percentage-30: .3;

  --percentage-35: .35;

  --percentage-40: .4;

  --percentage-45: .45;

  --percentage-50: .5;

  --percentage-55: .55;

  --percentage-60: .6;

  --percentage-65: .65;

  --percentage-70: .7;

  --percentage-75: .75;

  --percentage-80: .8;

  --percentage-85: .85;

  --percentage-90: .9;

  --percentage-95: .95;

  --percentage-100: 1;

  --percentage-translate-full: 1;

  --animate-in: enter var(--tw-duration,.15s)var(--tw-ease,ease);

  --animate-out: exit var(--tw-duration,.15s)var(--tw-ease,ease);

  --animate-accordion-down: accordion-down var(--tw-duration,.2s)ease-out;

  --animate-accordion-up: accordion-up var(--tw-duration,.2s)ease-out;

  --animate-caret-blink: caret-blink 1.25s ease-out infinite;

  @keyframes enter {
    from {
      opacity: var(--tw-enter-opacity,1);
      transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));
    }
  }

  @keyframes exit {
    to {
      opacity: var(--tw-exit-opacity,1);
      transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));
    }
  }

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--radix-accordion-content-height,var(--bits-accordion-content-height));
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height,var(--bits-accordion-content-height));
    }

    to {
      height: 0;
    }
  }

  @keyframes caret-blink {
    0%,70%,100% {
      opacity: 1;
    }

    20%,50% {
      opacity: 0;
    }
  }
}

@utility delay-*{
  animation-delay: calc(--value(number)*1ms);

  animation-delay: --value(--animation-delay-*,[duration],[*]);
}

@utility repeat-*{
  animation-iteration-count: --value(--animation-repeat-*,number);
}

@utility direction-*{
  animation-direction: --value(--animation-direction-*);
}

@utility fill-mode-*{
  animation-fill-mode: --value(--animation-fill-mode-*);
}

@utility running{
  animation-play-state: running;
}

@utility paused{
  animation-play-state: paused;
}

@utility fade-in{
  --tw-enter-opacity: 0;
}

@utility fade-in-*{
  --tw-enter-opacity: --value(--percentage-*,[*]);
}

@utility fade-out{
  --tw-exit-opacity: 0;
}

@utility fade-out-*{
  --tw-exit-opacity: --value(--percentage-*,[*]);
}

@utility zoom-in{
  --tw-enter-scale: 0;
}

@utility zoom-in-*{
  --tw-enter-scale: calc(--value([percentage])/100%);

  --tw-enter-scale: calc(--value([ratio],[number]));

  --tw-enter-scale: --value(--percentage-*);
}

@utility zoom-out{
  --tw-exit-scale: 0;
}

@utility zoom-out-*{
  --tw-exit-scale: calc(--value([percentage])/100%);

  --tw-exit-scale: calc(--value([ratio],[number]));

  --tw-exit-scale: --value(--percentage-*);
}

@utility spin-in{
  --tw-enter-rotate: 30deg;
}

@utility spin-in-*{
  --tw-enter-rotate: calc(--value(number)*1deg);

  --tw-enter-rotate: --value(--rotate-*,[angle]);
}

@utility spin-out{
  --tw-exit-rotate: 30deg;
}

@utility spin-out-*{
  --tw-exit-rotate: calc(--value(number)*1deg);

  --tw-exit-rotate: --value(--rotate-*,[angle]);
}

@utility slide-in-from-top{
  --tw-enter-translate-y: -100%;
}

@utility slide-in-from-top-*{
  --tw-enter-translate-y: calc(--value(integer)*var(--spacing)*-1);

  --tw-enter-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*-100%);

  --tw-enter-translate-y: calc(--value(ratio)*100%);

  --tw-enter-translate-y: calc(--value(--translate-*,[percentage],[length])*-1);
}

@utility slide-in-from-bottom{
  --tw-enter-translate-y: 100%;
}

@utility slide-in-from-bottom-*{
  --tw-enter-translate-y: calc(--value(integer)*var(--spacing));

  --tw-enter-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*100%);

  --tw-enter-translate-y: calc(--value(ratio)*100%);

  --tw-enter-translate-y: --value(--translate-*,[percentage],[length]);
}

@utility slide-in-from-left{
  --tw-enter-translate-x: -100%;
}

@utility slide-in-from-left-*{
  --tw-enter-translate-x: calc(--value(integer)*var(--spacing)*-1);

  --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%);

  --tw-enter-translate-x: calc(--value(ratio)*100%);

  --tw-enter-translate-x: calc(--value(--translate-*,[percentage],[length])*-1);
}

@utility slide-in-from-right{
  --tw-enter-translate-x: 100%;
}

@utility slide-in-from-right-*{
  --tw-enter-translate-x: calc(--value(integer)*var(--spacing));

  --tw-enter-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%);

  --tw-enter-translate-x: calc(--value(ratio)*100%);

  --tw-enter-translate-x: --value(--translate-*,[percentage],[length]);
}

@utility slide-out-to-top{
  --tw-exit-translate-y: -100%;
}

@utility slide-out-to-top-*{
  --tw-exit-translate-y: calc(--value(integer)*var(--spacing)*-1);

  --tw-exit-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*-100%);

  --tw-exit-translate-y: calc(--value(ratio)*100%);

  --tw-exit-translate-y: calc(--value(--translate-*,[percentage],[length])*-1);
}

@utility slide-out-to-bottom{
  --tw-exit-translate-y: 100%;
}

@utility slide-out-to-bottom-*{
  --tw-exit-translate-y: calc(--value(integer)*var(--spacing));

  --tw-exit-translate-y: calc(--value(--percentage-*,--percentage-translate-*)*100%);

  --tw-exit-translate-y: calc(--value(ratio)*100%);

  --tw-exit-translate-y: --value(--translate-*,[percentage],[length]);
}

@utility slide-out-to-left{
  --tw-exit-translate-x: -100%;
}

@utility slide-out-to-left-*{
  --tw-exit-translate-x: calc(--value(integer)*var(--spacing)*-1);

  --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*-100%);

  --tw-exit-translate-x: calc(--value(ratio)*100%);

  --tw-exit-translate-x: calc(--value(--translate-*,[percentage],[length])*-1);
}

@utility slide-out-to-right{
  --tw-exit-translate-x: 100%;
}

@utility slide-out-to-right-*{
  --tw-exit-translate-x: calc(--value(integer)*var(--spacing));

  --tw-exit-translate-x: calc(--value(--percentage-*,--percentage-translate-*)*100%);

  --tw-exit-translate-x: calc(--value(ratio)*100%);

  --tw-exit-translate-x: --value(--translate-*,[percentage],[length]);
}

/* Mejoras de accesibilidad para contraste de color */

/* Mejorar contraste de texto en elementos muted */

.text-gray-400 {
  color: #5a6377 !important;
  /* Más oscuro que el original para mejor contraste */
}

.text-gray-500 {
  color: #424a59 !important;
  /* Más oscuro que el original para mejor contraste */
}

.text-gray-600 {
  color: #374151 !important;
  /* Más oscuro para mejor contraste */
}

.text-gray-700 {
  color: #2d3748 !important;
  /* Asegurar buen contraste */
}

.text-muted-foreground {
  color: #424a59 !important;
  /* Más oscuro que el original para mejor contraste */
}

/* Mejorar contraste en botones */

.bg-indigo-600 {
  background-color: #4338ca !important;
  /* Más saturado para mejor contraste */
}

.text-indigo-200 {
  color: #e0e7ff !important;
  /* Más claro para mejor contraste sobre fondo oscuro */
}

/* Mejorar contraste en botones primarios */

.bg-primary {
  background-color: #0c8bd9 !important;
  /* Asegurar buen contraste */
}

.text-primary {
  color: #0c8bd9 !important;
  /* Asegurar buen contraste */
}

.bg-secondary {
  background-color: #6232c5 !important;
  /* Asegurar buen contraste */
}

.text-secondary {
  color: #6232c5 !important;
  /* Asegurar buen contraste */
}

/* Mejorar contraste en enlaces */

a {
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

a:focus {
  outline: 3px solid #1da1f2;
  outline-offset: 2px;
}

/* Mejorar contraste en inputs */

input:focus,
select:focus,
textarea:focus {
  outline: 3px solid #1da1f2;
  outline-offset: 2px;
}

/* Mejorar contraste en botones de acción */

button:focus {
  outline: 3px solid #1da1f2;
  outline-offset: 2px;
}

/* Mejorar contraste en estados hover */

.hover\:bg-gray-100:hover {
  background-color: #e5e7eb !important;
  /* Más oscuro para mejor contraste */
}

.hover\:text-gray-500:hover {
  color: #374151 !important;
  /* Más oscuro para mejor contraste */
}

/* Mejorar contraste en elementos de navegación */

.text-white {
  color: #ffffff !important;
}

.bg-indigo-700 {
  background-color: #4338ca !important;
  /* Más saturado para mejor contraste */
}

/* Mejorar contraste en elementos de formulario */

.placeholder-gray-400::-moz-placeholder {
  color: #5a6377 !important;
  /* Más oscuro para mejor contraste */
}

.placeholder-gray-400::placeholder {
  color: #5a6377 !important;
  /* Más oscuro para mejor contraste */
}

/* Mejorar contraste en tarjetas */

.shadow-sm {
  box-shadow:
    0 1px 2px 0 rgba(0, 0, 0, 0.1),
    0 1px 3px 0 rgba(0, 0, 0, 0.06) !important;
}

.shadow-md {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.15),
    0 2px 4px -1px rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.15),
    0 4px 6px -2px rgba(0, 0, 0, 0.1) !important;
}

/* Mejorar contraste en iconos */

.text-blue-600 {
  color: #1c64f2 !important;
  /* Más oscuro para mejor contraste */
}

.text-indigo-600 {
  color: #4f46e5 !important;
  /* Más oscuro para mejor contraste */
}

.text-purple-600 {
  color: #7e3af2 !important;
  /* Más oscuro para mejor contraste */
}

.text-primary {
  color: #0c8bd9 !important;
  /* Más oscuro para mejor contraste */
}

/* Mejorar contraste en fondos */

.bg-blue-100 {
  background-color: #dbeafe !important;
  /* Más oscuro para mejor contraste */
}

.bg-indigo-100 {
  background-color: #e0e7ff !important;
  /* Más oscuro para mejor contraste */
}

.bg-purple-100 {
  background-color: #ede9fe !important;
  /* Más oscuro para mejor contraste */
}

/* Mejorar accesibilidad para lectores de pantalla */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Mejorar contraste en botones de acción */

.btn-gradient {
  background: linear-gradient(90deg, #0c8bd9 0%, #6232c5 100%) !important;
  color: #ffffff !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.btn-gradient:hover {
  background: linear-gradient(90deg, #0a77ba 0%, #5429b3 100%) !important;
  box-shadow: 0 4px 12px rgba(12, 139, 217, 0.4) !important;
}

.btn-gradient:focus {
  outline: 3px solid #1da1f2 !important;
  outline-offset: 2px !important;
}

/**
 * Enhanced Theme System for QR CURSE
 *
 * This file provides an enhanced theme system with comprehensive CSS variables
 * for light and dark modes, semantic colors, and improved accessibility.
 *
 * Features:
 * - Comprehensive color system with semantic meanings
 * - Improved contrast ratios for accessibility
 * - Consistent spacing and typography scales
 * - Enhanced component-specific variables
 * - Smooth transitions between themes
 */

/* ============================================================================
 * Root Theme Variables (Light Mode)
 * ============================================================================ */

:root {
  /* Color System - Light Mode */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* Surface Colors */
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  /* Brand Colors (QR CURSE) */
  --primary: 210 100% 20%;
  /* CSI Blue #003366 */
  --primary-foreground: 0 0% 100%;
  --secondary: 38 92% 58%;
  /* CSI Yellow #F9B233 */
  --secondary-foreground: 210 100% 20%;
  /* Semantic Colors */
  --success: 142 76% 36%;
  --success-foreground: 0 0% 100%;
  --warning: 38 92% 50%;
  --warning-foreground: 222.2 84% 4.9%;
  --error: 0 84% 60%;
  --error-foreground: 0 0% 100%;
  --info: 199 89% 48%;
  --info-foreground: 0 0% 100%;
  /* Interactive States */
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  /* Borders and Inputs */
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 210 100% 20%;
  /* Destructive Actions */
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  /* Chart Colors */
  --chart-1: 210 100% 20%;
  --chart-2: 38 92% 58%;
  --chart-3: 199 89% 48%;
  --chart-4: 142 76% 36%;
  --chart-5: 0 84% 60%;
  /* Sidebar/Navigation */
  --sidebar: 0 0% 100%;
  --sidebar-foreground: 222.2 84% 4.9%;
  --sidebar-primary: 210 100% 20%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 210 40% 96%;
  --sidebar-accent-foreground: 222.2 84% 4.9%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --sidebar-ring: 210 100% 20%;
  /* Enhanced Component Variables */
  --header-background: 0 0% 100%;
  --header-foreground: 222.2 84% 4.9%;
  --header-border: 214.3 31.8% 91.4%;
  --footer-background: 210 40% 96%;
  --footer-foreground: 215.4 16.3% 46.9%;
  --table-header: 210 40% 96%;
  --table-row-even: 0 0% 100%;
  --table-row-odd: 210 40% 98%;
  --table-border: 214.3 31.8% 91.4%;
  /* Form Elements */
  --form-background: 0 0% 100%;
  --form-border: 214.3 31.8% 91.4%;
  --form-border-focus: 210 100% 20%;
  --form-placeholder: 215.4 16.3% 46.9%;
  /* Status Indicators */
  --status-online: 142 76% 36%;
  --status-offline: 215.4 16.3% 46.9%;
  --status-busy: 38 92% 50%;
  --status-away: 0 84% 60%;
  /* Design System */
  --radius: 0.5rem;
  --radius-sm: calc(var(--radius) - 0.125rem);
  --radius-md: var(--radius);
  --radius-lg: calc(var(--radius) + 0.125rem);
  --radius-xl: calc(var(--radius) + 0.25rem);
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg:
    0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl:
    0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  /* Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  /* Spacing Scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  /* Animation Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  /* 2025 UI Trends - Glassmorphism Support (Light Mode) */
  --glass-background: 0 0% 100% / 0.8;
  --glass-border: 222.2 84% 4.9% / 0.1;
  --glass-backdrop-blur: 12px;
  /* Neumorphism Support (Light Mode) */
  --neuro-shadow-light: 0 0% 100%;
  --neuro-shadow-dark: 214.3 31.8% 85%;
  --neuro-inset-light: 0 0% 100% / 0.8;
  --neuro-inset-dark: 214.3 31.8% 85% / 0.3;
}

/* ============================================================================
 * Dark Theme Variables (Optimized for 2025 UI/UX Trends)
 * ============================================================================ */

.dark {
  /* Color System - Dark Mode (Enhanced Contrast & Legibility) */
  --background: 224 71% 4%;
  /* Deeper, richer dark background */
  --foreground: 213 31% 91%;
  /* Higher contrast foreground */
  /* Surface Colors (Layered depth system) */
  --card: 224 71% 6%;
  /* Slightly elevated from background */
  --card-foreground: 213 31% 91%;
  --popover: 224 71% 8%;
  /* More elevated for popover hierarchy */
  --popover-foreground: 213 31% 91%;
  /* Brand Colors (2025 Dark Mode Optimized) */
  --primary: 217 91% 60%;
  /* Vibrant blue with excellent contrast */
  --primary-foreground: 224 71% 4%;
  --secondary: 47 96% 53%;
  /* Warm, accessible yellow */
  --secondary-foreground: 224 71% 4%;
  /* Semantic Colors (WCAG 2.1 AA Compliant) */
  --success: 142 69% 58%;
  /* Brighter green for better visibility */
  --success-foreground: 224 71% 4%;
  --warning: 38 92% 50%;
  /* Balanced orange-yellow */
  --warning-foreground: 224 71% 4%;
  --error: 0 91% 71%;
  /* High contrast red */
  --error-foreground: 224 71% 4%;
  --info: 199 89% 48%;
  /* Clear info blue */
  --info-foreground: 224 71% 4%;
  /* Interactive States (Enhanced hover/focus states) */
  --muted: 215 28% 17%;
  /* Subtle background for muted elements */
  --muted-foreground: 217 10% 64%;
  /* Readable muted text */
  --accent: 215 28% 17%;
  /* Consistent with muted for harmony */
  --accent-foreground: 213 31% 91%;
  /* Borders and Inputs (Improved definition) */
  --border: 215 28% 17%;
  /* Subtle but visible borders */
  --input: 215 28% 17%;
  /* Input field backgrounds */
  --ring: 217 91% 60%;
  /* Focus ring matches primary */
  /* Destructive Actions (High visibility) */
  --destructive: 0 91% 71%;
  --destructive-foreground: 224 71% 4%;
  /* Chart Colors (Dark Mode - Vibrant & Accessible) */
  --chart-1: 217 91% 60%;
  /* Primary blue */
  --chart-2: 47 96% 53%;
  /* Warm yellow */
  --chart-3: 142 69% 58%;
  /* Success green */
  --chart-4: 0 91% 71%;
  /* Error red */
  --chart-5: 271 91% 65%;
  /* Purple accent */
  /* Sidebar/Navigation (Modern Dark Design) */
  --sidebar: 224 71% 4%;
  /* Match main background */
  --sidebar-foreground: 213 31% 91%;
  --sidebar-primary: 217 91% 60%;
  /* Vibrant primary */
  --sidebar-primary-foreground: 224 71% 4%;
  --sidebar-accent: 215 28% 17%;
  /* Subtle accent */
  --sidebar-accent-foreground: 213 31% 91%;
  --sidebar-border: 215 28% 17%;
  /* Subtle borders */
  --sidebar-ring: 217 91% 60%;
  /* Enhanced Component Variables (Consistent Hierarchy) */
  --header-background: 224 71% 4%;
  --header-foreground: 213 31% 91%;
  --header-border: 215 28% 17%;
  --footer-background: 215 28% 17%;
  --footer-foreground: 217 10% 64%;
  --table-header: 215 28% 17%;
  --table-row-even: 224 71% 4%;
  --table-row-odd: 215 28% 8%;
  --table-border: 215 28% 17%;
  /* Form Elements (Enhanced Focus States) */
  --form-background: 224 71% 4%;
  --form-border: 215 28% 17%;
  --form-border-focus: 217 91% 60%;
  --form-placeholder: 217 10% 64%;
  /* Status Indicators (High Contrast) */
  --status-online: 142 69% 58%;
  --status-offline: 217 10% 64%;
  --status-busy: 47 96% 53%;
  --status-away: 0 91% 71%;
  /* Shadows (Enhanced Depth for Dark Mode) */
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 8px -2px rgb(0 0 0 / 0.5), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg:
    0 10px 20px -4px rgb(0 0 0 / 0.6), 0 4px 8px -4px rgb(0 0 0 / 0.4);
  --shadow-xl:
    0 25px 35px -8px rgb(0 0 0 / 0.7), 0 8px 15px -6px rgb(0 0 0 / 0.5);
  /* 2025 UI Trends - Glassmorphism Support */
  --glass-background: 224 71% 4% / 0.8;
  --glass-border: 213 31% 91% / 0.1;
  --glass-backdrop-blur: 12px;
  /* Neumorphism Support */
  --neuro-shadow-light: 215 28% 25%;
  --neuro-shadow-dark: 224 71% 2%;
  --neuro-inset-light: 215 28% 25% / 0.1;
  --neuro-inset-dark: 224 71% 2% / 0.3;
}

/* ============================================================================
 * Base Styles with Theme Variables
 * ============================================================================ */

/* Disable transitions during theme change to prevent flash - Higher specificity first */

.theme-transitioning *,
.theme-transitioning *::before,
.theme-transitioning *::after {
  transition: none !important;
}

/* Smooth theme transitions - only apply after initial load */

html.loaded *,
html.loaded *::before,
html.loaded *::after {
  transition-property:
    background-color, border-color, color, fill, stroke, box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--duration-fast);
}

/* Prevent FOUC by hiding content until theme is applied */
/* Temporarily disabled to fix white screen issue */
/*
html:not(.loaded) body {
  visibility: hidden;
}

html.loaded body {
  visibility: visible;
}
*/

/* ============================================================================
 * WCAG 2.1 AA Compliance Verification
 * ============================================================================ */

/*
 * Color Contrast Ratios (WCAG 2.1 AA requires 4.5:1 for normal text, 3:1 for large text)
 *
 * Light Mode:
 * - background (0 0% 100%) vs foreground (222.2 84% 4.9%) = ~15.8:1 ✅
 * - primary (210 100% 20%) vs primary-foreground (0 0% 100%) = ~8.2:1 ✅
 * - muted (210 40% 96%) vs muted-foreground (215.4 16.3% 46.9%) = ~4.6:1 ✅
 *
 * Dark Mode:
 * - background (224 71% 4%) vs foreground (213 31% 91%) = ~16.2:1 ✅
 * - primary (217 91% 60%) vs primary-foreground (224 71% 4%) = ~9.1:1 ✅
 * - muted (215 28% 17%) vs muted-foreground (217 10% 64%) = ~4.8:1 ✅
 *
 * All combinations exceed WCAG 2.1 AA requirements
 */

/* ============================================================================
 * 2025 UI/UX Trend Utilities
 * ============================================================================ */

/* Glassmorphism Effects */

.glass {
  background: hsl(var(--glass-background));
  border: 1px solid hsl(var(--glass-border));
  backdrop-filter: blur(var(--glass-backdrop-blur));
  -webkit-backdrop-filter: blur(var(--glass-backdrop-blur));
}

.glass-subtle {
  background: hsl(var(--background) / 0.6);
  border: 1px solid hsl(var(--border) / 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.glass-card {
  background: hsl(var(--card) / 0.8);
  border: 1px solid hsl(var(--border) / 0.2);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow:
    0 8px 32px hsl(var(--foreground) / 0.1),
    inset 0 1px 0 hsl(var(--foreground) / 0.1);
}

.glass-nav {
  background: hsl(var(--background) / 0.9);
  border-bottom: 1px solid hsl(var(--border) / 0.3);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: 0 4px 16px hsl(var(--foreground) / 0.05);
}

.glass-modal {
  background: hsl(var(--popover) / 0.95);
  border: 1px solid hsl(var(--border) / 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 20px 40px hsl(var(--foreground) / 0.15),
    inset 0 1px 0 hsl(var(--foreground) / 0.1);
}

/* Neumorphism Effects */

.neuro {
  background: hsl(var(--background));
  box-shadow:
    8px 8px 16px hsl(var(--neuro-shadow-dark)),
    -8px -8px 16px hsl(var(--neuro-shadow-light));
  border: none;
}

.neuro-inset {
  background: hsl(var(--background));
  box-shadow:
    inset 4px 4px 8px hsl(var(--neuro-inset-dark)),
    inset -4px -4px 8px hsl(var(--neuro-inset-light));
  border: none;
}

.neuro-subtle {
  background: hsl(var(--background));
  box-shadow:
    4px 4px 8px hsl(var(--neuro-shadow-dark) / 0.3),
    -4px -4px 8px hsl(var(--neuro-shadow-light) / 0.8);
  border: none;
}

/* Modern Card Styles */

.card-modern {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) + 0.25rem);
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.card-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: hsl(var(--primary) / 0.3);
}

/* Enhanced Interactive States */

.interactive {
  transition: all var(--duration-fast) cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.interactive:active {
  transform: translateY(0);
  transition-duration: calc(var(--duration-fast) / 2);
}

/* Micro-animations */

@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade-in-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-up {
  from {
    transform: translateY(30px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-down {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scale-in {
  from {
    transform: scale(0.8);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }

  50% {
    transform: scale(1.05);
  }

  70% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-4px);
  }
}

@keyframes bounce-strong {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-8px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-slide-in-right {
  animation: slide-in-right var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-left {
  animation: slide-in-left var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fade-in-up {
  animation: fade-in-up var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-up {
  animation: slide-in-up var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in-down {
  animation: slide-in-down var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
  animation: scale-in var(--duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-bounce-in {
  animation: bounce-in var(--duration-slow) cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-bounce-subtle {
  animation: bounce-subtle 0.6s ease-in-out;
}

.animate-bounce-strong {
  animation: bounce-strong 0.6s ease-in-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200px 100%;
}

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

* {
  border-color: hsl(var(--border));
}

html,
  body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family:
      "Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
      Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  min-height: 100vh;
}

body {
  display: flex;
  flex-direction: column;
}

h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
  font-family: "Montserrat", sans-serif;
  color: hsl(var(--foreground));
}

a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

main {
  flex: 1;
}

footer {
  margin-top: auto;
}

.container{
  width: 100%;
}

@media (min-width: 640px){
  .container{
    max-width: 640px;
  }
}

@media (min-width: 768px){
  .container{
    max-width: 768px;
  }
}

@media (min-width: 1024px){
  .container{
    max-width: 1024px;
  }
}

@media (min-width: 1280px){
  .container{
    max-width: 1280px;
  }
}

@media (min-width: 1536px){
  .container{
    max-width: 1536px;
  }
}

.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none{
  pointer-events: none;
}

.pointer-events-auto{
  pointer-events: auto;
}

.collapse{
  visibility: collapse;
}

.static{
  position: static;
}

.fixed{
  position: fixed;
}

.absolute{
  position: absolute;
}

.relative{
  position: relative;
}

.sticky{
  position: sticky;
}

.inset-0{
  inset: 0px;
}

.-right-1{
  right: -0.25rem;
}

.-top-1{
  top: -0.25rem;
}

.bottom-0{
  bottom: 0px;
}

.bottom-4{
  bottom: 1rem;
}

.bottom-6{
  bottom: 1.5rem;
}

.bottom-full{
  bottom: 100%;
}

.left-0{
  left: 0px;
}

.left-1\/2{
  left: 50%;
}

.left-2{
  left: 0.5rem;
}

.left-3{
  left: 0.75rem;
}

.left-6{
  left: 1.5rem;
}

.left-\[50\%\]{
  left: 50%;
}

.left-full{
  left: 100%;
}

.right-0{
  right: 0px;
}

.right-1\.5{
  right: 0.375rem;
}

.right-2{
  right: 0.5rem;
}

.right-3{
  right: 0.75rem;
}

.right-4{
  right: 1rem;
}

.right-6{
  right: 1.5rem;
}

.right-full{
  right: 100%;
}

.top-0{
  top: 0px;
}

.top-1\.5{
  top: 0.375rem;
}

.top-1\/2{
  top: 50%;
}

.top-14{
  top: 3.5rem;
}

.top-2{
  top: 0.5rem;
}

.top-20{
  top: 5rem;
}

.top-4{
  top: 1rem;
}

.top-\[50\%\]{
  top: 50%;
}

.top-full{
  top: 100%;
}

.z-10{
  z-index: 10;
}

.z-40{
  z-index: 40;
}

.z-50{
  z-index: 50;
}

.z-\[100\]{
  z-index: 100;
}

.z-\[110\]{
  z-index: 110;
}

.col-span-3{
  grid-column: span 3 / span 3;
}

.col-start-2{
  grid-column-start: 2;
}

.-mx-1{
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-auto{
  margin-left: auto;
  margin-right: auto;
}

.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.-mr-12{
  margin-right: -3rem;
}

.mb-1{
  margin-bottom: 0.25rem;
}

.mb-10{
  margin-bottom: 2.5rem;
}

.mb-12{
  margin-bottom: 3rem;
}

.mb-2{
  margin-bottom: 0.5rem;
}

.mb-3{
  margin-bottom: 0.75rem;
}

.mb-4{
  margin-bottom: 1rem;
}

.mb-6{
  margin-bottom: 1.5rem;
}

.mb-8{
  margin-bottom: 2rem;
}

.ml-1{
  margin-left: 0.25rem;
}

.ml-2{
  margin-left: 0.5rem;
}

.ml-3{
  margin-left: 0.75rem;
}

.ml-4{
  margin-left: 1rem;
}

.ml-6{
  margin-left: 1.5rem;
}

.ml-auto{
  margin-left: auto;
}

.mr-1{
  margin-right: 0.25rem;
}

.mr-1\.5{
  margin-right: 0.375rem;
}

.mr-2{
  margin-right: 0.5rem;
}

.mr-3{
  margin-right: 0.75rem;
}

.mr-4{
  margin-right: 1rem;
}

.mr-6{
  margin-right: 1.5rem;
}

.mt-0\.5{
  margin-top: 0.125rem;
}

.mt-1{
  margin-top: 0.25rem;
}

.mt-12{
  margin-top: 3rem;
}

.mt-2{
  margin-top: 0.5rem;
}

.mt-3{
  margin-top: 0.75rem;
}

.mt-4{
  margin-top: 1rem;
}

.mt-6{
  margin-top: 1.5rem;
}

.mt-8{
  margin-top: 2rem;
}

.mt-auto{
  margin-top: auto;
}

.line-clamp-1{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.block{
  display: block;
}

.inline-block{
  display: inline-block;
}

.inline{
  display: inline;
}

.flex{
  display: flex;
}

.inline-flex{
  display: inline-flex;
}

.table{
  display: table;
}

.grid{
  display: grid;
}

.\!contents{
  display: contents !important;
}

.contents{
  display: contents;
}

.hidden{
  display: none;
}

.aspect-\[3\/4\]{
  aspect-ratio: 3/4;
}

.aspect-\[4\/3\]{
  aspect-ratio: 4/3;
}

.aspect-square{
  aspect-ratio: 1 / 1;
}

.aspect-video{
  aspect-ratio: 16 / 9;
}

.size-2{
  width: 0.5rem;
  height: 0.5rem;
}

.size-3\.5{
  width: 0.875rem;
  height: 0.875rem;
}

.size-4{
  width: 1rem;
  height: 1rem;
}

.size-9{
  width: 2.25rem;
  height: 2.25rem;
}

.h-0{
  height: 0px;
}

.h-0\.5{
  height: 0.125rem;
}

.h-1{
  height: 0.25rem;
}

.h-10{
  height: 2.5rem;
}

.h-11{
  height: 2.75rem;
}

.h-12{
  height: 3rem;
}

.h-14{
  height: 3.5rem;
}

.h-16{
  height: 4rem;
}

.h-2{
  height: 0.5rem;
}

.h-20{
  height: 5rem;
}

.h-24{
  height: 6rem;
}

.h-3{
  height: 0.75rem;
}

.h-3\.5{
  height: 0.875rem;
}

.h-32{
  height: 8rem;
}

.h-4{
  height: 1rem;
}

.h-48{
  height: 12rem;
}

.h-5{
  height: 1.25rem;
}

.h-6{
  height: 1.5rem;
}

.h-64{
  height: 16rem;
}

.h-7{
  height: 1.75rem;
}

.h-72{
  height: 18rem;
}

.h-8{
  height: 2rem;
}

.h-9{
  height: 2.25rem;
}

.h-\[1\.2rem\]{
  height: 1.2rem;
}

.h-\[1px\]{
  height: 1px;
}

.h-\[calc\(100vh-3\.5rem\)\]{
  height: calc(100vh - 3.5rem);
}

.h-\[var\(--radix-select-trigger-height\)\]{
  height: var(--radix-select-trigger-height);
}

.h-auto{
  height: auto;
}

.h-full{
  height: 100%;
}

.h-px{
  height: 1px;
}

.max-h-16{
  max-height: 4rem;
}

.max-h-96{
  max-height: 24rem;
}

.max-h-\[300px\]{
  max-height: 300px;
}

.max-h-screen{
  max-height: 100vh;
}

.min-h-4{
  min-height: 1rem;
}

.min-h-\[300px\]{
  min-height: 300px;
}

.min-h-\[400px\]{
  min-height: 400px;
}

.min-h-\[80px\]{
  min-height: 80px;
}

.min-h-screen{
  min-height: 100vh;
}

.w-0{
  width: 0px;
}

.w-1{
  width: 0.25rem;
}

.w-1\/2{
  width: 50%;
}

.w-10{
  width: 2.5rem;
}

.w-12{
  width: 3rem;
}

.w-14{
  width: 3.5rem;
}

.w-16{
  width: 4rem;
}

.w-2{
  width: 0.5rem;
}

.w-2\/3{
  width: 66.666667%;
}

.w-20{
  width: 5rem;
}

.w-24{
  width: 6rem;
}

.w-3{
  width: 0.75rem;
}

.w-3\.5{
  width: 0.875rem;
}

.w-3\/4{
  width: 75%;
}

.w-32{
  width: 8rem;
}

.w-36{
  width: 9rem;
}

.w-4{
  width: 1rem;
}

.w-5{
  width: 1.25rem;
}

.w-56{
  width: 14rem;
}

.w-6{
  width: 1.5rem;
}

.w-60{
  width: 15rem;
}

.w-64{
  width: 16rem;
}

.w-7{
  width: 1.75rem;
}

.w-72{
  width: 18rem;
}

.w-8{
  width: 2rem;
}

.w-9{
  width: 2.25rem;
}

.w-\[1\.2rem\]{
  width: 1.2rem;
}

.w-\[1px\]{
  width: 1px;
}

.w-\[200px\]{
  width: 200px;
}

.w-\[var\(--radix-popover-trigger-width\)\]{
  width: var(--radix-popover-trigger-width);
}

.w-auto{
  width: auto;
}

.w-full{
  width: 100%;
}

.min-w-0{
  min-width: 0px;
}

.min-w-\[140px\]{
  min-width: 140px;
}

.min-w-\[200px\]{
  min-width: 200px;
}

.min-w-\[300px\]{
  min-width: 300px;
}

.min-w-\[8rem\]{
  min-width: 8rem;
}

.min-w-\[var\(--radix-select-trigger-width\)\]{
  min-width: var(--radix-select-trigger-width);
}

.min-w-full{
  min-width: 100%;
}

.max-w-2xl{
  max-width: 42rem;
}

.max-w-3xl{
  max-width: 48rem;
}

.max-w-4xl{
  max-width: 56rem;
}

.max-w-6xl{
  max-width: 72rem;
}

.max-w-7xl{
  max-width: 80rem;
}

.max-w-\[120px\]{
  max-width: 120px;
}

.max-w-\[450px\]{
  max-width: 450px;
}

.max-w-full{
  max-width: 100%;
}

.max-w-lg{
  max-width: 32rem;
}

.max-w-md{
  max-width: 28rem;
}

.max-w-none{
  max-width: none;
}

.max-w-screen-2xl{
  max-width: 1536px;
}

.max-w-screen-lg{
  max-width: 1024px;
}

.max-w-screen-md{
  max-width: 768px;
}

.max-w-screen-sm{
  max-width: 640px;
}

.max-w-screen-xl{
  max-width: 1280px;
}

.max-w-xl{
  max-width: 36rem;
}

.max-w-xs{
  max-width: 20rem;
}

.flex-1{
  flex: 1 1 0%;
}

.flex-shrink-0{
  flex-shrink: 0;
}

.shrink-0{
  flex-shrink: 0;
}

.flex-grow{
  flex-grow: 1;
}

.caption-bottom{
  caption-side: bottom;
}

.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-\[-50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4{
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-8{
  --tw-translate-y: 2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[-50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0{
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90{
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-0{
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes fade-in-up{
  from{
    transform: translateY(20px);
    opacity: 0;
  }

  to{
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fade-in-up{
  animation: fade-in-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes ping{
  75%, 100%{
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping{
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes pulse{
  50%{
    opacity: .5;
  }
}

.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-subtle{
  0%, 100%{
    opacity: 1;
  }

  50%{
    opacity: 0.8;
  }
}

.animate-pulse-subtle{
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes slide-in-left{
  from{
    transform: translateX(-100%);
    opacity: 0;
  }

  to{
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-left{
  animation: slide-in-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slide-in-right{
  from{
    transform: translateX(100%);
    opacity: 0;
  }

  to{
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right{
  animation: slide-in-right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}

.animate-spin{
  animation: spin 1s linear infinite;
}

.cursor-default{
  cursor: default;
}

.cursor-not-allowed{
  cursor: not-allowed;
}

.cursor-pointer{
  cursor: pointer;
}

.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.resize-none{
  resize: none;
}

.\!resize{
  resize: both !important;
}

.resize{
  resize: both;
}

.list-inside{
  list-style-position: inside;
}

.list-disc{
  list-style-type: disc;
}

.appearance-none{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-3{
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-\[0_1fr\]{
  grid-template-columns: 0 1fr;
}

.flex-row{
  flex-direction: row;
}

.flex-col{
  flex-direction: column;
}

.flex-col-reverse{
  flex-direction: column-reverse;
}

.flex-wrap{
  flex-wrap: wrap;
}

.items-start{
  align-items: flex-start;
}

.items-end{
  align-items: flex-end;
}

.items-center{
  align-items: center;
}

.items-stretch{
  align-items: stretch;
}

.justify-start{
  justify-content: flex-start;
}

.justify-end{
  justify-content: flex-end;
}

.justify-center{
  justify-content: center;
}

.justify-between{
  justify-content: space-between;
}

.justify-around{
  justify-content: space-around;
}

.justify-evenly{
  justify-content: space-evenly;
}

.justify-items-start{
  justify-items: start;
}

.gap-1{
  gap: 0.25rem;
}

.gap-1\.5{
  gap: 0.375rem;
}

.gap-12{
  gap: 3rem;
}

.gap-2{
  gap: 0.5rem;
}

.gap-3{
  gap: 0.75rem;
}

.gap-4{
  gap: 1rem;
}

.gap-6{
  gap: 1.5rem;
}

.gap-8{
  gap: 2rem;
}

.gap-y-0\.5{
  row-gap: 0.125rem;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}

.overflow-auto{
  overflow: auto;
}

.overflow-hidden{
  overflow: hidden;
}

.overflow-x-auto{
  overflow-x: auto;
}

.overflow-y-auto{
  overflow-y: auto;
}

.overflow-x-hidden{
  overflow-x: hidden;
}

.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis{
  text-overflow: ellipsis;
}

.whitespace-nowrap{
  white-space: nowrap;
}

.rounded{
  border-radius: 0.25rem;
}

.rounded-2xl{
  border-radius: 1rem;
}

.rounded-3xl{
  border-radius: 1.5rem;
}

.rounded-\[inherit\]{
  border-radius: inherit;
}

.rounded-full{
  border-radius: 9999px;
}

.rounded-lg{
  border-radius: var(--radius);
}

.rounded-md{
  border-radius: calc(var(--radius) - 2px);
}

.rounded-none{
  border-radius: 0px;
}

.rounded-sm{
  border-radius: calc(var(--radius) - 4px);
}

.rounded-xl{
  border-radius: 0.75rem;
}

.rounded-b-lg{
  border-bottom-right-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

.rounded-b-none{
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}

.rounded-l-none{
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

.rounded-r-full{
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.rounded-r-none{
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

.rounded-t-none{
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.border{
  border-width: 1px;
}

.border-2{
  border-width: 2px;
}

.border-b{
  border-bottom-width: 1px;
}

.border-b-2{
  border-bottom-width: 2px;
}

.border-l-2{
  border-left-width: 2px;
}

.border-l-4{
  border-left-width: 4px;
}

.border-r{
  border-right-width: 1px;
}

.border-t{
  border-top-width: 1px;
}

.border-t-2{
  border-top-width: 2px;
}

.border-dashed{
  border-style: dashed;
}

.border-none{
  border-style: none;
}

.border-black{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.border-blue-300{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}

.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-border{
  border-color: hsl(var(--border));
}

.border-border\/50{
  border-color: hsl(var(--border) / 0.5);
}

.border-destructive{
  border-color: hsl(var(--destructive));
}

.border-destructive\/30{
  border-color: hsl(var(--destructive) / 0.3);
}

.border-glass-border{
  border-color: hsl(var(--glass-border));
}

.border-gray-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-gray-700{
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.border-gray-800{
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}

.border-gray-900{
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}

.border-green-300{
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}

.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}

.border-input{
  border-color: hsl(var(--input));
}

.border-primary{
  border-color: hsl(var(--primary));
}

.border-primary\/20{
  border-color: hsl(var(--primary) / 0.2);
}

.border-primary\/30{
  border-color: hsl(var(--primary) / 0.3);
}

.border-primary\/50{
  border-color: hsl(var(--primary) / 0.5);
}

.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}

.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.border-sidebar-border{
  border-color: hsl(var(--sidebar-border));
}

.border-success{
  border-color: hsl(var(--success));
}

.border-transparent{
  border-color: transparent;
}

.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/20{
  border-color: rgb(255 255 255 / 0.2);
}

.border-white\/30{
  border-color: rgb(255 255 255 / 0.3);
}

.border-yellow-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}

.border-yellow-300{
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));
}

.border-yellow-500{
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}

.border-b-border{
  border-bottom-color: hsl(var(--border));
}

.border-t-transparent{
  border-top-color: transparent;
}

.bg-accent{
  background-color: hsl(var(--accent));
}

.bg-background{
  background-color: hsl(var(--background));
}

.bg-background\/50{
  background-color: hsl(var(--background) / 0.5);
}

.bg-background\/80{
  background-color: hsl(var(--background) / 0.8);
}

.bg-background\/95{
  background-color: hsl(var(--background) / 0.95);
}

.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/30{
  background-color: rgb(0 0 0 / 0.3);
}

.bg-black\/40{
  background-color: rgb(0 0 0 / 0.4);
}

.bg-black\/50{
  background-color: rgb(0 0 0 / 0.5);
}

.bg-black\/80{
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-border{
  background-color: hsl(var(--border));
}

.bg-card{
  background-color: hsl(var(--card));
}

.bg-destructive{
  background-color: hsl(var(--destructive));
}

.bg-destructive\/10{
  background-color: hsl(var(--destructive) / 0.1);
}

.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-700{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.bg-gray-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.bg-gray-900{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-green-400{
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}

.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-indigo-100{
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}

.bg-indigo-50{
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}

.bg-indigo-600{
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.bg-muted{
  background-color: hsl(var(--muted));
}

.bg-muted\/50{
  background-color: hsl(var(--muted) / 0.5);
}

.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}

.bg-pink-100{
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));
}

.bg-pink-50{
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity, 1));
}

.bg-popover{
  background-color: hsl(var(--popover));
}

.bg-primary{
  background-color: hsl(var(--primary));
}

.bg-primary\/10{
  background-color: hsl(var(--primary) / 0.1);
}

.bg-primary\/5{
  background-color: hsl(var(--primary) / 0.05);
}

.bg-purple-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.bg-purple-600{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-400{
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}

.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.bg-red-50\/50{
  background-color: rgb(254 242 242 / 0.5);
}

.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-secondary{
  background-color: hsl(var(--secondary));
}

.bg-sidebar-border{
  background-color: hsl(var(--sidebar-border));
}

.bg-sidebar-primary{
  background-color: hsl(var(--sidebar-primary));
}

.bg-sidebar-primary-foreground{
  background-color: hsl(var(--sidebar-primary-foreground));
}

.bg-sidebar\/95{
  background-color: hsl(var(--sidebar) / 0.95);
}

.bg-success{
  background-color: hsl(var(--success));
}

.bg-transparent{
  background-color: transparent;
}

.bg-warning{
  background-color: hsl(var(--warning));
}

.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/20{
  background-color: rgb(255 255 255 / 0.2);
}

.bg-white\/30{
  background-color: rgb(255 255 255 / 0.3);
}

.bg-white\/90{
  background-color: rgb(255 255 255 / 0.9);
}

.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}

.bg-yellow-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}

.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}

.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-50{
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-green-50{
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary{
  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/10{
  --tw-gradient-from: hsl(var(--primary) / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary\/5{
  --tw-gradient-from: hsl(var(--primary) / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-transparent{
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-secondary\/5{
  --tw-gradient-to: hsl(var(--secondary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--secondary) / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/20{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-blue-50{
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}

.to-indigo-50{
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}

.to-primary\/5{
  --tw-gradient-to: hsl(var(--primary) / 0.05) var(--tw-gradient-to-position);
}

.to-secondary{
  --tw-gradient-to: hsl(var(--secondary)) var(--tw-gradient-to-position);
}

.to-secondary\/10{
  --tw-gradient-to: hsl(var(--secondary) / 0.1) var(--tw-gradient-to-position);
}

.to-secondary\/5{
  --tw-gradient-to: hsl(var(--secondary) / 0.05) var(--tw-gradient-to-position);
}

.to-transparent{
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.bg-clip-text{
  -webkit-background-clip: text;
          background-clip: text;
}

.fill-current{
  fill: currentColor;
}

.object-contain{
  -o-object-fit: contain;
     object-fit: contain;
}

.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}

.object-fill{
  -o-object-fit: fill;
     object-fit: fill;
}

.object-none{
  -o-object-fit: none;
     object-fit: none;
}

.p-0{
  padding: 0px;
}

.p-0\.5{
  padding: 0.125rem;
}

.p-1{
  padding: 0.25rem;
}

.p-1\.5{
  padding: 0.375rem;
}

.p-10{
  padding: 2.5rem;
}

.p-2{
  padding: 0.5rem;
}

.p-3{
  padding: 0.75rem;
}

.p-4{
  padding: 1rem;
}

.p-5{
  padding: 1.25rem;
}

.p-6{
  padding: 1.5rem;
}

.p-8{
  padding: 2rem;
}

.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-1\.5{
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-2{
  padding-bottom: 0.5rem;
}

.pb-3{
  padding-bottom: 0.75rem;
}

.pb-4{
  padding-bottom: 1rem;
}

.pl-1{
  padding-left: 0.25rem;
}

.pl-10{
  padding-left: 2.5rem;
}

.pl-3{
  padding-left: 0.75rem;
}

.pl-8{
  padding-left: 2rem;
}

.pr-10{
  padding-right: 2.5rem;
}

.pr-2{
  padding-right: 0.5rem;
}

.pr-4{
  padding-right: 1rem;
}

.pr-8{
  padding-right: 2rem;
}

.pt-0{
  padding-top: 0px;
}

.pt-0\.5{
  padding-top: 0.125rem;
}

.pt-1{
  padding-top: 0.25rem;
}

.pt-2{
  padding-top: 0.5rem;
}

.pt-4{
  padding-top: 1rem;
}

.pt-5{
  padding-top: 1.25rem;
}

.pt-6{
  padding-top: 1.5rem;
}

.pt-8{
  padding-top: 2rem;
}

.text-left{
  text-align: left;
}

.text-center{
  text-align: center;
}

.text-right{
  text-align: right;
}

.align-middle{
  vertical-align: middle;
}

.font-body{
  font-family: Open Sans, sans-serif;
}

.font-heading{
  font-family: Montserrat, sans-serif;
}

.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.text-2xl{
  font-size: var(--font-size-2xl);
}

.text-3xl{
  font-size: var(--font-size-3xl);
}

.text-4xl{
  font-size: var(--font-size-4xl);
}

.text-\[10px\]{
  font-size: 10px;
}

.text-base{
  font-size: var(--font-size-base);
}

.text-lg{
  font-size: var(--font-size-lg);
}

.text-sm{
  font-size: var(--font-size-sm);
}

.text-xl{
  font-size: var(--font-size-xl);
}

.text-xs{
  font-size: var(--font-size-xs);
}

.font-bold{
  font-weight: 700;
}

.font-extrabold{
  font-weight: 800;
}

.font-medium{
  font-weight: 500;
}

.font-normal{
  font-weight: 400;
}

.font-semibold{
  font-weight: 600;
}

.uppercase{
  text-transform: uppercase;
}

.capitalize{
  text-transform: capitalize;
}

.leading-none{
  line-height: 1;
}

.leading-relaxed{
  line-height: 1.625;
}

.leading-tight{
  line-height: 1.25;
}

.tracking-tight{
  letter-spacing: -0.025em;
}

.tracking-wider{
  letter-spacing: 0.05em;
}

.tracking-widest{
  letter-spacing: 0.1em;
}

.text-accent-foreground{
  color: hsl(var(--accent-foreground));
}

.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.text-blue-900{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}

.text-card-foreground{
  color: hsl(var(--card-foreground));
}

.text-destructive{
  color: hsl(var(--destructive));
}

.text-destructive-foreground{
  color: hsl(var(--destructive-foreground));
}

.text-error{
  color: hsl(var(--error));
}

.text-foreground{
  color: hsl(var(--foreground));
}

.text-foreground\/50{
  color: hsl(var(--foreground) / 0.5);
}

.text-foreground\/60{
  color: hsl(var(--foreground) / 0.6);
}

.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-green-700{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}

.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.text-green-900{
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}

.text-indigo-600{
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.text-indigo-700{
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}

.text-indigo-800{
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.text-muted-foreground{
  color: hsl(var(--muted-foreground));
}

.text-muted-foreground\/50{
  color: hsl(var(--muted-foreground) / 0.5);
}

.text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}

.text-pink-600{
  --tw-text-opacity: 1;
  color: rgb(219 39 119 / var(--tw-text-opacity, 1));
}

.text-pink-800{
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}

.text-popover-foreground{
  color: hsl(var(--popover-foreground));
}

.text-primary{
  color: hsl(var(--primary));
}

.text-primary-foreground{
  color: hsl(var(--primary-foreground));
}

.text-primary\/60{
  color: hsl(var(--primary) / 0.6);
}

.text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-purple-800{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}

.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.text-secondary-foreground{
  color: hsl(var(--secondary-foreground));
}

.text-sidebar-foreground{
  color: hsl(var(--sidebar-foreground));
}

.text-sidebar-foreground\/60{
  color: hsl(var(--sidebar-foreground) / 0.6);
}

.text-sidebar-foreground\/70{
  color: hsl(var(--sidebar-foreground) / 0.7);
}

.text-sidebar-primary-foreground{
  color: hsl(var(--sidebar-primary-foreground));
}

.text-success{
  color: hsl(var(--success));
}

.text-transparent{
  color: transparent;
}

.text-warning{
  color: hsl(var(--warning));
}

.text-warning-foreground{
  color: hsl(var(--warning-foreground));
}

.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/90{
  color: rgb(255 255 255 / 0.9);
}

.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}

.text-yellow-600{
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}

.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}

.underline-offset-4{
  text-underline-offset: 4px;
}

.antialiased{
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.placeholder-gray-400::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.placeholder-gray-400::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.placeholder-muted-foreground::-moz-placeholder{
  color: hsl(var(--muted-foreground));
}

.placeholder-muted-foreground::placeholder{
  color: hsl(var(--muted-foreground));
}

.opacity-0{
  opacity: 0;
}

.opacity-100{
  opacity: 1;
}

.opacity-20{
  opacity: 0.2;
}

.opacity-25{
  opacity: 0.25;
}

.opacity-30{
  opacity: 0.3;
}

.opacity-50{
  opacity: 0.5;
}

.opacity-70{
  opacity: 0.7;
}

.opacity-75{
  opacity: 0.75;
}

.opacity-80{
  opacity: 0.8;
}

.opacity-90{
  opacity: 0.9;
}

.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0_0_30px_rgba\(59\2c 130\2c 246\2c 0\.3\)\]{
  --tw-shadow: 0 0 30px rgba(59,130,246,0.3);
  --tw-shadow-colored: 0 0 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg{
  --tw-shadow: var(--shadow-lg);
  --tw-shadow-colored: var(--shadow-lg);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md{
  --tw-shadow: var(--shadow-md);
  --tw-shadow-colored: var(--shadow-md);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm{
  --tw-shadow: var(--shadow-sm);
  --tw-shadow-colored: var(--shadow-sm);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl{
  --tw-shadow: var(--shadow-xl);
  --tw-shadow-colored: var(--shadow-xl);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline{
  outline-style: solid;
}

.ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-offset-background{
  --tw-ring-offset-color: hsl(var(--background));
}

.brightness-0{
  --tw-brightness: brightness(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow{
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-md{
  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-sm{
  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert{
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur{
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-glass{
  --tw-backdrop-blur: blur(var(--glass-backdrop-blur));
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-lg{
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter{
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[color\2c box-shadow\]{
  transition-property: color,box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow{
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-150{
  transition-duration: 150ms;
}

.duration-200{
  transition-duration: 200ms;
}

.duration-300{
  transition-duration: 300ms;
}

.duration-500{
  transition-duration: 500ms;
}

.duration-700{
  transition-duration: 700ms;
}

.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

/* Legacy styles - now handled by theme variables in @layer base */

.button-primary {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-family: "Montserrat", sans-serif;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 700;
  transition: background 0.2s;
}

.button-primary:hover {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

/* Dark mode variant handled by Tailwind CSS automatically */

@theme inline {
  --color-background: var(--background);

  --color-foreground: var(--foreground);

  --font-sans: var(--font-geist-sans);

  --font-mono: var(--font-geist-mono);

  --color-sidebar-ring: var(--sidebar-ring);

  --color-sidebar-border: var(--sidebar-border);

  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);

  --color-sidebar-accent: var(--sidebar-accent);

  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);

  --color-sidebar-primary: var(--sidebar-primary);

  --color-sidebar-foreground: var(--sidebar-foreground);

  --color-sidebar: var(--sidebar);

  --color-chart-5: var(--chart-5);

  --color-chart-4: var(--chart-4);

  --color-chart-3: var(--chart-3);

  --color-chart-2: var(--chart-2);

  --color-chart-1: var(--chart-1);

  --color-ring: var(--ring);

  --color-input: var(--input);

  --color-border: var(--border);

  --color-destructive: var(--destructive);

  --color-accent-foreground: var(--accent-foreground);

  --color-accent: var(--accent);

  --color-muted-foreground: var(--muted-foreground);

  --color-muted: var(--muted);

  --color-secondary-foreground: var(--secondary-foreground);

  --color-secondary: var(--secondary);

  --color-primary-foreground: var(--primary-foreground);

  --color-primary: var(--primary);

  --color-popover-foreground: var(--popover-foreground);

  --color-popover: var(--popover);

  --color-card-foreground: var(--card-foreground);

  --color-card: var(--card);

  --radius-sm: calc(var(--radius) - 4px);

  --radius-md: calc(var(--radius) - 2px);

  --radius-lg: var(--radius);

  --radius-xl: calc(var(--radius) + 4px);
}

/* CSS variables are now defined in enhanced-theme.css */

/* Animaciones y efectos */

@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out forwards;
}

.hero-gradient {
  background: linear-gradient(135deg, #1da1f2 0%, #7b3fe4 100%);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hero-gradient::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* Eliminada la imagen de fondo */
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.2;
  z-index: 0;
}

.btn-gradient {
  background: linear-gradient(90deg, #1da1f2 0%, #7b3fe4 100%);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(90deg, #0c8bd9 0%, #6232c5 100%);
  box-shadow: 0 4px 12px rgba(29, 161, 242, 0.3);
}

.subtle-pattern {
  background-image: radial-gradient(
    rgba(29, 161, 242, 0.05) 1px,
    transparent 1px
  );
  background-size: 20px 20px;
}

/*
  Inicialmente visible para el renderizado del servidor,
  luego se oculta en el cliente y se muestra con animación al hacer scroll
*/

.section-fade {
  opacity: 1;
  /* Inicialmente visible para SSR */
  transform: translateY(0);
  transition:
    opacity 0.6s ease,
    transform 0.6s ease;
}

/* En el cliente, después de la hidratación, se oculta y luego se muestra con animación */

html.js .section-fade:not(.visible) {
  opacity: 0.01;
  transform: translateY(20px);
}

.section-fade.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Asegurar que en el servidor y al imprimir se renderice con opacidad 1 */

@media print {
  .section-fade {
    opacity: 1;
    transform: none;
  }
}

.card-hover:hover {
  transform: scale(1.02);
  transition: all 0.3s ease;
}

input:focus,
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(29, 161, 242, 0.3);
}

/* --- FIX: Variables críticas para evitar pantalla blanca --- */

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 210 100% 20%;
  --primary-foreground: 0 0% 100%;
  --secondary: 38 92% 58%;
  --secondary-foreground: 210 100% 20%;
}

.selection\:bg-primary *::-moz-selection{
  background-color: hsl(var(--primary));
}

.selection\:bg-primary *::selection{
  background-color: hsl(var(--primary));
}

.selection\:text-primary-foreground *::-moz-selection{
  color: hsl(var(--primary-foreground));
}

.selection\:text-primary-foreground *::selection{
  color: hsl(var(--primary-foreground));
}

.selection\:bg-primary::-moz-selection{
  background-color: hsl(var(--primary));
}

.selection\:bg-primary::selection{
  background-color: hsl(var(--primary));
}

.selection\:text-primary-foreground::-moz-selection{
  color: hsl(var(--primary-foreground));
}

.selection\:text-primary-foreground::selection{
  color: hsl(var(--primary-foreground));
}

.file\:inline-flex::file-selector-button{
  display: inline-flex;
}

.file\:h-7::file-selector-button{
  height: 1.75rem;
}

.file\:border-0::file-selector-button{
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button{
  background-color: transparent;
}

.file\:text-sm::file-selector-button{
  font-size: var(--font-size-sm);
}

.file\:font-medium::file-selector-button{
  font-weight: 500;
}

.file\:text-foreground::file-selector-button{
  color: hsl(var(--foreground));
}

.placeholder\:text-muted-foreground::-moz-placeholder{
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder{
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-white\/60::-moz-placeholder{
  color: rgb(255 255 255 / 0.6);
}

.placeholder\:text-white\/60::placeholder{
  color: rgb(255 255 255 / 0.6);
}

.hover\:-translate-y-0\.5:hover{
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-2:hover{
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-3:hover{
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:rotate-1:hover{
  --tw-rotate: 1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:rotate-2:hover{
  --tw-rotate: 2deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:rotate-3:hover{
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover{
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce{
  0%, 100%{
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50%{
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

.hover\:animate-bounce:hover{
  animation: bounce 1s infinite;
}

.hover\:border-primary\/20:hover{
  border-color: hsl(var(--primary) / 0.2);
}

.hover\:border-primary\/50:hover{
  border-color: hsl(var(--primary) / 0.5);
}

.hover\:border-b-primary\/50:hover{
  border-bottom-color: hsl(var(--primary) / 0.5);
}

.hover\:bg-accent:hover{
  background-color: hsl(var(--accent));
}

.hover\:bg-blue-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-destructive\/80:hover{
  background-color: hsl(var(--destructive) / 0.8);
}

.hover\:bg-destructive\/90:hover{
  background-color: hsl(var(--destructive) / 0.9);
}

.hover\:bg-glass-background\/80:hover{
  background-color: hsl(var(--glass-background) / 0.8);
}

.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-muted:hover{
  background-color: hsl(var(--muted));
}

.hover\:bg-muted\/30:hover{
  background-color: hsl(var(--muted) / 0.3);
}

.hover\:bg-muted\/50:hover{
  background-color: hsl(var(--muted) / 0.5);
}

.hover\:bg-muted\/80:hover{
  background-color: hsl(var(--muted) / 0.8);
}

.hover\:bg-primary:hover{
  background-color: hsl(var(--primary));
}

.hover\:bg-primary\/5:hover{
  background-color: hsl(var(--primary) / 0.05);
}

.hover\:bg-primary\/80:hover{
  background-color: hsl(var(--primary) / 0.8);
}

.hover\:bg-primary\/90:hover{
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-purple-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary:hover{
  background-color: hsl(var(--secondary));
}

.hover\:bg-secondary\/80:hover{
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:bg-sidebar-accent:hover{
  background-color: hsl(var(--sidebar-accent));
}

.hover\:bg-white\/10:hover{
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:from-primary\/90:hover{
  --tw-gradient-from: hsl(var(--primary) / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-secondary\/90:hover{
  --tw-gradient-to: hsl(var(--secondary) / 0.9) var(--tw-gradient-to-position);
}

.hover\:text-accent-foreground:hover{
  color: hsl(var(--accent-foreground));
}

.hover\:text-foreground:hover{
  color: hsl(var(--foreground));
}

.hover\:text-foreground\/80:hover{
  color: hsl(var(--foreground) / 0.8);
}

.hover\:text-gray-500:hover{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-600:hover{
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-800:hover{
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover{
  color: hsl(var(--primary));
}

.hover\:text-primary\/80:hover{
  color: hsl(var(--primary) / 0.8);
}

.hover\:text-red-700:hover{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-secondary:hover{
  color: hsl(var(--secondary));
}

.hover\:text-sidebar-accent-foreground:hover{
  color: hsl(var(--sidebar-accent-foreground));
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.hover\:opacity-100:hover{
  opacity: 1;
}

.hover\:opacity-80:hover{
  opacity: 0.8;
}

.hover\:shadow-2xl:hover{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_0_20px_rgba\(59\2c 130\2c 246\2c 0\.2\)\]:hover{
  --tw-shadow: 0 0 20px rgba(59,130,246,0.2);
  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_0_30px_rgba\(59\2c 130\2c 246\2c 0\.3\)\]:hover{
  --tw-shadow: 0 0 30px rgba(59,130,246,0.3);
  --tw-shadow-colored: 0 0 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_0_30px_rgba\(59\2c 130\2c 246\2c 0\.4\)\]:hover{
  --tw-shadow: 0 0 30px rgba(59,130,246,0.4);
  --tw-shadow-colored: 0 0 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_0_40px_rgba\(59\2c 130\2c 246\2c 0\.4\)\]:hover{
  --tw-shadow: 0 0 40px rgba(59,130,246,0.4);
  --tw-shadow-colored: 0 0 40px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_0_40px_rgba\(59\2c 130\2c 246\2c 0\.5\)\]:hover{
  --tw-shadow: 0 0 40px rgba(59,130,246,0.5);
  --tw-shadow-colored: 0 0 40px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover{
  --tw-shadow: var(--shadow-lg);
  --tw-shadow-colored: var(--shadow-lg);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover{
  --tw-shadow: var(--shadow-md);
  --tw-shadow-colored: var(--shadow-md);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover{
  --tw-shadow: var(--shadow-xl);
  --tw-shadow-colored: var(--shadow-xl);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:not-sr-only:focus{
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.focus\:absolute:focus{
  position: absolute;
}

.focus\:left-4:focus{
  left: 1rem;
}

.focus\:top-4:focus{
  top: 1rem;
}

.focus\:z-50:focus{
  z-index: 50;
}

.focus\:border-indigo-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.focus\:border-red-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.focus\:border-transparent:focus{
  border-color: transparent;
}

.focus\:bg-accent:focus{
  background-color: hsl(var(--accent));
}

.focus\:text-accent-foreground:focus{
  color: hsl(var(--accent-foreground));
}

.focus\:text-red-600:focus{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.focus\:opacity-100:focus{
  opacity: 1;
}

.focus\:shadow-lg:focus{
  --tw-shadow: var(--shadow-lg);
  --tw-shadow-colored: var(--shadow-lg);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-inset:focus{
  --tw-ring-inset: inset;
}

.focus\:ring-indigo-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary:focus{
  --tw-ring-color: hsl(var(--primary));
}

.focus\:ring-red-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-ring:focus{
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-white\/50:focus{
  --tw-ring-color: rgb(255 255 255 / 0.5);
}

.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}

.focus-visible\:border-primary:focus-visible{
  border-color: hsl(var(--primary));
}

.focus-visible\:border-ring:focus-visible{
  border-color: hsl(var(--ring));
}

.focus-visible\:border-b-primary:focus-visible{
  border-bottom-color: hsl(var(--primary));
}

.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-0:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-\[3px\]:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-destructive:focus-visible{
  --tw-ring-color: hsl(var(--destructive));
}

.focus-visible\:ring-destructive\/20:focus-visible{
  --tw-ring-color: hsl(var(--destructive) / 0.2);
}

.focus-visible\:ring-ring:focus-visible{
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-ring\/50:focus-visible{
  --tw-ring-color: hsl(var(--ring) / 0.5);
}

.focus-visible\:ring-success:focus-visible{
  --tw-ring-color: hsl(var(--success));
}

.focus-visible\:ring-offset-0:focus-visible{
  --tw-ring-offset-width: 0px;
}

.focus-visible\:ring-offset-2:focus-visible{
  --tw-ring-offset-width: 2px;
}

.focus-visible\:ring-offset-background:focus-visible{
  --tw-ring-offset-color: hsl(var(--background));
}

.active\:scale-95:active{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.active\:scale-\[0\.98\]:active{
  --tw-scale-x: 0.98;
  --tw-scale-y: 0.98;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled{
  opacity: 0.5;
}

.group:hover .group-hover\:block{
  display: block;
}

.group:hover .group-hover\:translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:text-foreground{
  color: hsl(var(--foreground));
}

.group:hover .group-hover\:text-primary{
  color: hsl(var(--primary));
}

.group:hover .group-hover\:opacity-100{
  opacity: 1;
}

.group:hover .group-hover\:shadow-md{
  --tw-shadow: var(--shadow-md);
  --tw-shadow-colored: var(--shadow-md);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group.destructive .group-\[\.destructive\]\:border-muted\/40{
  border-color: hsl(var(--muted) / 0.4);
}

.group.destructive .group-\[\.destructive\]\:text-red-300{
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:hover{
  border-color: hsl(var(--destructive) / 0.3);
}

.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover{
  background-color: hsl(var(--destructive));
}

.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover{
  color: hsl(var(--destructive-foreground));
}

.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover{
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus{
  --tw-ring-color: hsl(var(--destructive));
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}

.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus{
  --tw-ring-offset-color: #dc2626;
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-50{
  opacity: 0.5;
}

.has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]:has(>svg){
  grid-template-columns: calc(var(--spacing) * 4) 1fr;
}

.has-\[\>svg\]\:gap-x-3:has(>svg){
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}

.has-\[\>svg\]\:px-2\.5:has(>svg){
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.has-\[\>svg\]\:px-3:has(>svg){
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.has-\[\>svg\]\:px-4:has(>svg){
  padding-left: 1rem;
  padding-right: 1rem;
}

.aria-selected\:bg-accent[aria-selected="true"]{
  background-color: hsl(var(--accent));
}

.aria-selected\:text-accent-foreground[aria-selected="true"]{
  color: hsl(var(--accent-foreground));
}

.data-\[disabled\]\:pointer-events-none[data-disabled]{
  pointer-events: none;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"]{
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"]{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"]{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"]{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=checked\]\:translate-x-4[data-state="checked"]{
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"]{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"]{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"]{
  --tw-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"]{
  --tw-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[state\=active\]\:bg-background[data-state="active"]{
  background-color: hsl(var(--background));
}

.data-\[state\=checked\]\:bg-primary[data-state="checked"]{
  background-color: hsl(var(--primary));
}

.data-\[state\=open\]\:bg-accent[data-state="open"]{
  background-color: hsl(var(--accent));
}

.data-\[state\=selected\]\:bg-muted[data-state="selected"]{
  background-color: hsl(var(--muted));
}

.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"]{
  background-color: hsl(var(--input));
}

.data-\[inset\]\:pl-8[data-inset]{
  padding-left: 2rem;
}

.data-\[state\=active\]\:text-foreground[data-state="active"]{
  color: hsl(var(--foreground));
}

.data-\[state\=open\]\:text-accent-foreground[data-state="open"]{
  color: hsl(var(--accent-foreground));
}

.data-\[state\=open\]\:text-muted-foreground[data-state="open"]{
  color: hsl(var(--muted-foreground));
}

.data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"]{
  color: hsl(var(--destructive));
}

.data-\[disabled\]\:opacity-50[data-disabled]{
  opacity: 0.5;
}

.data-\[state\=active\]\:shadow-sm[data-state="active"]{
  --tw-shadow: var(--shadow-sm);
  --tw-shadow-colored: var(--shadow-sm);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[swipe\=move\]\:transition-none[data-swipe="move"]{
  transition-property: none;
}

.\*\:data-\[slot\=alert-description\]\:text-destructive\/90[data-slot="alert-description"] > *{
  color: hsl(var(--destructive) / 0.9);
}

.data-\[variant\=destructive\]\:focus\:bg-destructive\/10:focus[data-variant="destructive"]{
  background-color: hsl(var(--destructive) / 0.1);
}

.data-\[variant\=destructive\]\:focus\:text-destructive:focus[data-variant="destructive"]{
  color: hsl(var(--destructive));
}

.group[data-disabled="true"] .group-data-\[disabled\=true\]\:pointer-events-none{
  pointer-events: none;
}

.group[data-disabled="true"] .group-data-\[disabled\=true\]\:opacity-50{
  opacity: 0.5;
}

@supports ((-webkit-backdrop-filter: var(--tw)) or (backdrop-filter: var(--tw))){
  .supports-\[backdrop-filter\]\:bg-background\/60{
    background-color: hsl(var(--background) / 0.6);
  }
}

.dark\:-rotate-90:is(.dark *){
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:rotate-0:is(.dark *){
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:scale-0:is(.dark *){
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:scale-100:is(.dark *){
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:border-blue-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}

.dark\:border-green-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}

.dark\:border-input:is(.dark *){
  border-color: hsl(var(--input));
}

.dark\:bg-blue-900\/20:is(.dark *){
  background-color: rgb(30 58 138 / 0.2);
}

.dark\:bg-destructive\/60:is(.dark *){
  background-color: hsl(var(--destructive) / 0.6);
}

.dark\:bg-gray-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}

.dark\:bg-green-900\/20:is(.dark *){
  background-color: rgb(20 83 45 / 0.2);
}

.dark\:bg-input\/30:is(.dark *){
  background-color: hsl(var(--input) / 0.3);
}

.dark\:bg-red-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}

.dark\:bg-yellow-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
}

.dark\:text-blue-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:text-green-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}

.dark\:text-green-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}

.dark\:text-green-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.dark\:text-red-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}

.dark\:text-yellow-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}

.dark\:shadow-\[0_0_30px_rgba\(59\2c 130\2c 246\2c 0\.2\)\]:is(.dark *){
  --tw-shadow: 0 0 30px rgba(59,130,246,0.2);
  --tw-shadow-colored: 0 0 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:hover\:bg-accent\/50:hover:is(.dark *){
  background-color: hsl(var(--accent) / 0.5);
}

.dark\:hover\:bg-input\/50:hover:is(.dark *){
  background-color: hsl(var(--input) / 0.5);
}

.dark\:hover\:bg-muted\/10:hover:is(.dark *){
  background-color: hsl(var(--muted) / 0.1);
}

.dark\:focus-visible\:ring-destructive\/40:focus-visible:is(.dark *){
  --tw-ring-color: hsl(var(--destructive) / 0.4);
}

.dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:focus[data-variant="destructive"]:is(.dark *){
  background-color: hsl(var(--destructive) / 0.2);
}

@media (min-width: 640px){
  .sm\:bottom-0{
    bottom: 0px;
  }

  .sm\:right-0{
    right: 0px;
  }

  .sm\:top-auto{
    top: auto;
  }

  .sm\:mx-auto{
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:mb-\[1px\]{
    margin-bottom: 1px;
  }

  .sm\:mt-0{
    margin-top: 0px;
  }

  .sm\:block{
    display: block;
  }

  .sm\:flex{
    display: flex;
  }

  .sm\:hidden{
    display: none;
  }

  .sm\:w-48{
    width: 12rem;
  }

  .sm\:w-full{
    width: 100%;
  }

  .sm\:max-w-md{
    max-width: 28rem;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:flex-col{
    flex-direction: column;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:justify-end{
    justify-content: flex-end;
  }

  .sm\:justify-between{
    justify-content: space-between;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:rounded-lg{
    border-radius: var(--radius);
  }

  .sm\:px-10{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:pl-3{
    padding-left: 0.75rem;
  }

  .sm\:pr-12{
    padding-right: 3rem;
  }

  .sm\:pt-3{
    padding-top: 0.75rem;
  }

  .sm\:text-left{
    text-align: left;
  }

  .sm\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-sm{
    font-size: var(--font-size-sm);
  }
}

@media (min-width: 768px){
  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .md\:mb-0{
    margin-bottom: 0px;
  }

  .md\:block{
    display: block;
  }

  .md\:flex{
    display: flex;
  }

  .md\:hidden{
    display: none;
  }

  .md\:w-40{
    width: 10rem;
  }

  .md\:w-auto{
    width: auto;
  }

  .md\:w-full{
    width: 100%;
  }

  .md\:max-w-\[420px\]{
    max-width: 420px;
  }

  .md\:flex-none{
    flex: none;
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:flex-col{
    flex-direction: column;
  }

  .md\:py-24{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:py-32{
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .md\:text-2xl{
    font-size: var(--font-size-2xl);
  }

  .md\:text-3xl{
    font-size: var(--font-size-3xl);
  }

  .md\:text-4xl{
    font-size: var(--font-size-4xl);
  }

  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-lg{
    font-size: var(--font-size-lg);
  }

  .md\:text-sm{
    font-size: var(--font-size-sm);
  }

  .md\:text-xl{
    font-size: var(--font-size-xl);
  }
}

@media (min-width: 1024px){
  .lg\:fixed{
    position: fixed;
  }

  .lg\:inset-y-0{
    top: 0px;
    bottom: 0px;
  }

  .lg\:ml-16{
    margin-left: 4rem;
  }

  .lg\:ml-64{
    margin-left: 16rem;
  }

  .lg\:block{
    display: block;
  }

  .lg\:flex{
    display: flex;
  }

  .lg\:inline-flex{
    display: inline-flex;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:w-1\/2{
    width: 50%;
  }

  .lg\:w-64{
    width: 16rem;
  }

  .lg\:translate-x-0{
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:flex-row{
    flex-direction: row;
  }

  .lg\:flex-col{
    flex-direction: column;
  }

  .lg\:p-8{
    padding: 2rem;
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:pl-64{
    padding-left: 16rem;
  }

  .lg\:text-3xl{
    font-size: var(--font-size-3xl);
  }

  .lg\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-base{
    font-size: var(--font-size-base);
  }
}

@media (min-width: 1280px){
  .xl\:block{
    display: block;
  }

  .xl\:hidden{
    display: none;
  }
}

@media (min-width: 1536px){
  .\32xl\:block{
    display: block;
  }

  .\32xl\:hidden{
    display: none;
  }
}

@media print{
  .print\:hidden{
    display: none;
  }
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]){
  padding-right: 0px;
}

.\[\&\>span\]\:line-clamp-1>span{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\>svg\]\:size-4>svg{
  width: 1rem;
  height: 1rem;
}

.\[\&\>svg\]\:translate-y-0\.5>svg{
  --tw-translate-y: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&\>svg\]\:text-current>svg{
  color: currentColor;
}

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading]{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading]{
  font-size: var(--font-size-xs);
}

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading]{
  font-weight: 500;
}

.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading]{
  color: hsl(var(--muted-foreground));
}

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{
  padding-top: 0px;
}

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg{
  height: 1.25rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg{
  width: 1.25rem;
}

.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input]{
  height: 3rem;
}

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item]{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item]{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg{
  height: 1.25rem;
}

.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg{
  width: 1.25rem;
}

.\[\&_p\]\:leading-relaxed p{
  line-height: 1.625;
}

.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*='size-']){
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*='text-']){
  color: hsl(var(--muted-foreground));
}

.\[\&_svg\]\:pointer-events-none svg{
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg{
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg{
  flex-shrink: 0;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child{
  border-width: 0px;
}

.\[\&_tr\]\:border-b tr{
  border-bottom-width: 1px;
}
