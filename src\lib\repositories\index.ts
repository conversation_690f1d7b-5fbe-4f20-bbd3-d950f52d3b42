/**
 * Repository Factory and Exports
 *
 * This file provides a centralized way to create and access all repositories.
 * It implements the factory pattern to ensure consistent repository creation
 * and provides a single point of access for all domain repositories.
 */

import { database } from "../adapters";
import type { DatabaseAdapter } from "../adapters/database/types";

// Repository imports
import { BaseRepository } from "./base-repository";
import {
  CertificateRepository,
  type CertificateRepositoryInterface,
} from "./certificate-repository";
import {
  UserRepository,
  type UserRepositoryInterface,
} from "./user-repository";

// Additional repository interfaces (to be implemented)
export interface CourseRepositoryInterface {
  findByInstructor(instructorId: string): Promise<unknown[]>;
  findActiveCoursesForCompany(companyId: string): Promise<unknown[]>;
}

export interface GradeRepositoryInterface {
  findByUser(userId: string): Promise<unknown[]>;
  findByAssessment(assessmentId: string): Promise<unknown[]>;
}

export interface AttendanceRepositoryInterface {
  findByUser(userId: string): Promise<unknown[]>;
  findByLesson(lessonId: string): Promise<unknown[]>;
}

// Placeholder repository implementations (to be fully implemented later)
class CourseRepository
  extends BaseRepository<unknown>
  implements CourseRepositoryInterface
{
  protected tableName = "courses";

  async findByInstructor(instructorId: string): Promise<unknown[]> {
    return this.findMany([
      { column: "instructor_id", operator: "eq", value: instructorId },
    ]);
  }

  async findActiveCoursesForCompany(companyId: string): Promise<unknown[]> {
    return this.findMany([
      { column: "company_id", operator: "eq", value: companyId },
      { column: "status", operator: "eq", value: "active" },
    ]);
  }
}

class GradeRepository
  extends BaseRepository<unknown>
  implements GradeRepositoryInterface
{
  protected tableName = "grades";

  async findByUser(userId: string): Promise<unknown[]> {
    return this.findMany([this.userFilter(userId)]);
  }

  async findByAssessment(assessmentId: string): Promise<unknown[]> {
    return this.findMany([
      { column: "assessment_id", operator: "eq", value: assessmentId },
    ]);
  }
}

class AttendanceRepository
  extends BaseRepository<unknown>
  implements AttendanceRepositoryInterface
{
  protected tableName = "attendance";

  async findByUser(userId: string): Promise<unknown[]> {
    return this.findMany([this.userFilter(userId)]);
  }

  async findByLesson(lessonId: string): Promise<unknown[]> {
    return this.findMany([
      { column: "lesson_id", operator: "eq", value: lessonId },
    ]);
  }
}

// ============================================================================
// Repository Factory
// ============================================================================

export class RepositoryFactory {
  private userRepository?: UserRepository;
  private certificateRepository?: CertificateRepository;
  private courseRepository?: CourseRepository;
  private gradeRepository?: GradeRepository;
  private attendanceRepository?: AttendanceRepository;

  constructor(private db: DatabaseAdapter) {}

  // ============================================================================
  // Repository Getters (Singleton Pattern)
  // ============================================================================

  get users(): UserRepository {
    if (!this.userRepository) {
      this.userRepository = new UserRepository(this.db);
    }
    return this.userRepository;
  }

  get certificates(): CertificateRepository {
    if (!this.certificateRepository) {
      this.certificateRepository = new CertificateRepository(this.db);
    }
    return this.certificateRepository;
  }

  get courses(): CourseRepository {
    if (!this.courseRepository) {
      this.courseRepository = new CourseRepository(this.db);
    }
    return this.courseRepository;
  }

  get grades(): GradeRepository {
    if (!this.gradeRepository) {
      this.gradeRepository = new GradeRepository(this.db);
    }
    return this.gradeRepository;
  }

  get attendance(): AttendanceRepository {
    if (!this.attendanceRepository) {
      this.attendanceRepository = new AttendanceRepository(this.db);
    }
    return this.attendanceRepository;
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  /**
   * Reset all repository instances (useful for testing)
   */
  reset(): void {
    this.userRepository = undefined;
    this.certificateRepository = undefined;
    this.courseRepository = undefined;
    this.gradeRepository = undefined;
    this.attendanceRepository = undefined;
  }

  /**
   * Get all repository instances
   */
  getAllRepositories(): {
    users: UserRepository;
    certificates: CertificateRepository;
    courses: CourseRepository;
    grades: GradeRepository;
    attendance: AttendanceRepository;
  } {
    return {
      users: this.users,
      certificates: this.certificates,
      courses: this.courses,
      grades: this.grades,
      attendance: this.attendance,
    };
  }
}

// ============================================================================
// Default Repository Factory Instance
// ============================================================================

/**
 * Default repository factory using the default database adapter
 */
export const repositories = new RepositoryFactory(database);

// ============================================================================
// Convenience Exports
// ============================================================================

// Export individual repositories for direct access
export const userRepository = repositories.users;
export const certificateRepository = repositories.certificates;
export const courseRepository = repositories.courses;
export const gradeRepository = repositories.grades;
export const attendanceRepository = repositories.attendance;

// Export repository classes for custom instantiation
export {
  BaseRepository,
  UserRepository,
  CertificateRepository,
  CourseRepository,
  GradeRepository,
  AttendanceRepository,
};

// Export interfaces
export type {
  UserRepositoryInterface,
  CertificateRepositoryInterface,
  CourseRepositoryInterface,
  GradeRepositoryInterface,
  AttendanceRepositoryInterface,
};

// ============================================================================
// Migration Helpers
// ============================================================================

/**
 * Helper function to create a repository factory with a custom database adapter
 * Useful for testing or when using different database configurations
 */
export function createRepositoryFactory(
  db: DatabaseAdapter,
): RepositoryFactory {
  return new RepositoryFactory(db);
}

/**
 * Helper function to validate all repositories are properly configured
 */
export function validateRepositories(factory: RepositoryFactory): boolean {
  try {
    const repos = factory.getAllRepositories();

    // Basic validation - ensure all repositories are instantiated
    const requiredRepos = [
      "users",
      "certificates",
      "courses",
      "grades",
      "attendance",
    ];

    for (const repoName of requiredRepos) {
      const repo = repos[repoName as keyof typeof repos];
      if (!repo) {
        console.error(`Repository ${repoName} is not properly instantiated`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("Repository validation failed:", error);
    return false;
  }
}

// Validate repositories on initialization in development
if (process.env.NODE_ENV === "development") {
  if (!validateRepositories(repositories)) {
    console.error("Default repositories validation failed");
  }
}
