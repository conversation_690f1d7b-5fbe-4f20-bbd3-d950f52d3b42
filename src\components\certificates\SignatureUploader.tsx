"use client";

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Loader2, Upload, X } from "lucide-react";
import Image from "next/image";
import { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import {
  deleteFile,
  ensureBucketExists,
  uploadFile,
} from "@/lib/storage-utils";

interface SignatureUploaderProps {
  templateId: string;
  currentSignatureUrl?: string | null;
  onSignatureUploaded: (url: string) => void;
}

export default function SignatureUploader({
  templateId,
  currentSignatureUrl,
  onSignatureUploaded,
}: SignatureUploaderProps) {
  const _supabase = createClientComponentClient();
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(
    currentSignatureUrl || null,
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!["image/jpeg", "image/png"].includes(file.type)) {
      toast({
        title: "Formato no válido",
        description: "Por favor, sube una imagen en formato JPG o PNG.",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Archivo demasiado grande",
        description: "El tamaño máximo permitido es 5MB.",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);

      // Asegurarse de que el bucket 'signatures' existe
      const bucketResult = await ensureBucketExists("signatures", true);

      // Continuamos incluso si hay problemas con el bucket
      if (!bucketResult.success) {
        // Si el error es de permisos, intentamos subir el archivo de todos modos
        if (
          bucketResult.error?.message &&
          (bucketResult.error.message.includes("permission") ||
            bucketResult.error.message.includes("security policy") ||
            bucketResult.error.message.includes("not authorized"))
        ) {
          console.warn(
            `Intentando subir firma a pesar de problemas de permisos en bucket 'signatures'`,
          );
        } else {
          throw new Error(bucketResult.message);
        }
      }

      // Create a unique file name
      const fileExt = file.name.split(".").pop();
      const fileName = `template-${templateId}-signature-${Date.now()}.${fileExt}`;

      // Subir el archivo usando la utilidad
      const uploadResult = await uploadFile("signatures", fileName, file);

      if (!uploadResult.success) {
        // Si el error es de permisos, mostramos un mensaje más amigable
        if (
          uploadResult.error?.message &&
          (uploadResult.error.message.includes("permission") ||
            uploadResult.error.message.includes("security policy") ||
            uploadResult.error.message.includes("not authorized"))
        ) {
          throw new Error(
            "No tienes permisos suficientes para subir firmas. Contacta al administrador.",
          );
        } else {
          throw new Error(uploadResult.message);
        }
      }

      const publicUrl = uploadResult.publicUrl as string;

      // Update preview
      setPreviewUrl(publicUrl);

      // Call the callback with the new URL
      onSignatureUploaded(publicUrl);

      toast({
        title: "Firma subida",
        description: "La firma se ha subido correctamente.",
      });
    } catch (error) {
      const errMsg =
        error instanceof Error
          ? error.message
          : "No se pudo subir la firma. Intente nuevamente.";
      console.error("Error uploading signature:", error);
      toast({
        title: "Error al subir firma",
        description: errMsg,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveSignature = async () => {
    if (
      !previewUrl ||
      !confirm("¿Estás seguro de que deseas eliminar esta firma?")
    ) {
      return;
    }

    try {
      setUploading(true);

      // Verificar si la URL es válida
      if (!previewUrl.includes("/")) {
        throw new Error("URL de firma inválida");
      }

      // Extract file path from URL
      const urlParts = previewUrl.split("/");
      const filePath = urlParts[urlParts.length - 1];

      if (!filePath) {
        throw new Error("No se pudo extraer el nombre del archivo de la URL");
      }

      console.log("Intentando eliminar archivo:", filePath);

      // Verificar si el bucket existe
      const bucketResult = await ensureBucketExists("signatures", true);

      if (!bucketResult.success) {
        // Si el bucket no existe, consideramos que el archivo ya está eliminado
        console.warn(
          "El bucket 'signatures' no existe, considerando archivo como ya eliminado",
        );
        setPreviewUrl(null);
        onSignatureUploaded("");

        toast({
          title: "Firma eliminada",
          description: "La firma ha sido eliminada.",
        });
        return;
      }

      // Eliminar el archivo usando la utilidad
      const deleteResult = await deleteFile("signatures", filePath);

      // Incluso si hay un error, limpiamos la URL de la firma
      setPreviewUrl(null);
      onSignatureUploaded("");

      if (!deleteResult.success) {
        // Si el error es que no se encuentra el archivo, consideramos que ya está eliminado
        if (
          deleteResult.message &&
          (deleteResult.message.includes("not found") ||
            deleteResult.message.includes("no existe") ||
            deleteResult.message.includes("not exist"))
        ) {
          toast({
            title: "Firma eliminada",
            description: "La firma ha sido eliminada.",
          });
        } else {
          console.warn(
            "Error al eliminar archivo, pero continuamos:",
            deleteResult.message,
          );
          toast({
            title: "Firma eliminada",
            description:
              "La firma ha sido eliminada, pero hubo un problema con el servidor.",
          });
        }
      } else {
        toast({
          title: "Firma eliminada",
          description: "La firma se ha eliminado correctamente.",
        });
      }
    } catch (error) {
      const errMsg =
        error instanceof Error
          ? error.message
          : "No se pudo eliminar la firma. Intente nuevamente.";
      console.error("Error removing signature:", error);
      toast({
        title: "Error al eliminar firma",
        description: errMsg,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Firma para el Certificado</CardTitle>
        <CardDescription>
          Sube una imagen de la firma para mostrar en los certificados (JPG o
          PNG)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {previewUrl ? (
            <div className="relative border rounded-md p-4 flex flex-col items-center">
              <div className="relative w-full h-32 mb-2 bg-gray-50">
                <Image
                  src={previewUrl}
                  alt="Firma para el certificado"
                  fill
                  style={{ objectFit: "contain" }}
                  onError={(e) => {
                    console.error("Error loading signature image");
                    // Cambiar a una imagen de placeholder
                    e.currentTarget.src =
                      "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2YxZjFmMSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGFsaWdubWVudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjOTk5Ij5FcnJvciBhbCBjYXJnYXIgaW1hZ2VuPC90ZXh0Pjwvc3ZnPg==";
                  }}
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploading}
                >
                  {uploading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Cambiar firma
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleRemoveSignature}
                  disabled={uploading}
                >
                  {uploading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <X className="h-4 w-4 mr-2" />
                  )}
                  Eliminar
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center border border-dashed rounded-md p-6">
              <Upload className="h-10 w-10 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground mb-4">
                Haz clic para subir una imagen de firma (JPG o PNG)
              </p>
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploading}
              >
                {uploading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : null}
                Seleccionar archivo
              </Button>
              <Input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png"
                onChange={handleFileChange}
                className="hidden"
              />
            </div>
          )}

          <div className="text-xs text-gray-500 mt-2">
            <p>• Formatos aceptados: JPG, PNG</p>
            <p>• Tamaño máximo: 5MB</p>
            <p>
              • Recomendación: Utilice una imagen con fondo transparente (PNG)
              para mejores resultados
            </p>
            <p>• La firma se mostrará en la parte inferior del certificado</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
